# EditorConfig configuration for Multi-Agent Real-Estate Contract Platform
# Ensures consistent coding styles across different editors and IDEs

root = true

# Default settings for all files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# Python files
[*.py]
indent_size = 4
max_line_length = 88

# JavaScript/TypeScript files
[*.{js,jsx,ts,tsx}]
indent_size = 2
max_line_length = 100

# JSON files
[*.json]
indent_size = 2

# YAML files
[*.{yml,yaml}]
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false
max_line_length = 120

# Configuration files
[*.{toml,ini,cfg}]
indent_size = 4

# Docker files
[Dockerfile*]
indent_size = 4

# Shell scripts
[*.{sh,bash}]
indent_size = 2

# SQL files
[*.sql]
indent_size = 2

# HTML files
[*.html]
indent_size = 2

# CSS/SCSS files
[*.{css,scss,sass}]
indent_size = 2

# XML files
[*.xml]
indent_size = 2
