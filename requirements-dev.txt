# Development dependencies for Multi-Agent Real-Estate Contract Platform
# Install with: pip install -r requirements-dev.txt

# Code Quality and Formatting
pre-commit==4.2.0
black==25.1.0
isort==6.0.1
flake8==7.3.0
mypy==1.17.1
bandit==1.8.6

# Security
detect-secrets==1.5.0

# Testing
pytest==8.3.4
pytest-asyncio==0.25.0
pytest-cov==6.0.0
pytest-mock==3.14.0
httpx==0.28.1  # For testing FastAPI endpoints

# Documentation
sphinx==8.1.3
sphinx-rtd-theme==3.0.2

# Development utilities
ipython==8.31.0
rich==14.1.0  # Better terminal output
python-dotenv==1.0.1  # Environment variable management
