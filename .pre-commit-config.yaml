# Pre-commit configuration for Multi-Agent Real-Estate Contract Platform
# Ensures code quality and consistency across Python and TypeScript/JavaScript

repos:
  # General file formatting and checks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      - id: trailing-whitespace
        exclude: ^.*\.(md|rst)$
      - id: end-of-file-fixer
        exclude: ^.*\.(md|rst)$
      - id: check-yaml
        args: ["--unsafe"]
      - id: check-json
      - id: check-toml
      - id: check-xml
      - id: check-merge-conflict
      - id: check-case-conflict
      - id: check-added-large-files
        args: ["--maxkb=1000"]
      - id: check-docstring-first
      - id: debug-statements
      - id: mixed-line-ending
        args: ["--fix=lf"]

  # Python code formatting with Black
  - repo: https://github.com/psf/black
    rev: 24.10.0
    hooks:
      - id: black
        language_version: python3
        files: ^backend/.*\.py$

  # Python import sorting with isort
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        args: ["--profile", "black", "--line-length", "88"]
        files: ^backend/.*\.py$

  # Python linting with flake8
  - repo: https://github.com/pycqa/flake8
    rev: 7.1.1
    hooks:
      - id: flake8
        args:
          [
            "--max-line-length=88",
            "--extend-ignore=E203,W503,E501",
            "--exclude=migrations,__pycache__,.git,venv,.venv",
          ]
        files: ^backend/.*\.py$

  # Python type checking with mypy
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.14.0
    hooks:
      - id: mypy
        args: [--ignore-missing-imports, --no-strict-optional]
        files: ^backend/.*\.py$
        additional_dependencies: [types-all]

  # Python security linting with bandit
  - repo: https://github.com/pycqa/bandit
    rev: 1.7.10
    hooks:
      - id: bandit
        args: ["-r", "backend/", "-f", "json", "-o", "bandit-report.json"]
        files: ^backend/.*\.py$

  # JavaScript/TypeScript formatting with Prettier
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v4.0.0-alpha.8
    hooks:
      - id: prettier
        files: ^frontend/.*\.(js|jsx|ts|tsx|json|css|scss|md)$
        exclude: ^frontend/(node_modules|\.next|dist)/

  # JavaScript/TypeScript linting with ESLint
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v9.17.0
    hooks:
      - id: eslint
        files: ^frontend/.*\.(js|jsx|ts|tsx)$
        exclude: ^frontend/(node_modules|\.next|dist)/
        additional_dependencies:
          - eslint@^9.17.0
          - "@typescript-eslint/eslint-plugin@^8.18.2"
          - "@typescript-eslint/parser@^8.18.2"
          - "eslint-config-next@^15.1.3"

  # Docker file linting
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
        args: ["--ignore", "DL3008", "--ignore", "DL3009"]

  # YAML formatting and linting
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.35.1
    hooks:
      - id: yamllint
        args: [-c=.yamllint.yaml]

  # Markdown linting
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.42.0
    hooks:
      - id: markdownlint
        args: ["--fix"]
        exclude: ^(CHANGELOG\.md|node_modules/)

  # Shell script linting (disabled on Windows)
  # - repo: https://github.com/shellcheck-py/shellcheck-py
  #   rev: v0.10.0.1
  #   hooks:
  #     - id: shellcheck

  # Secrets detection
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.5.0
    hooks:
      - id: detect-secrets
        args: ["--baseline", ".secrets.baseline"]
        exclude: ^(\.git/|node_modules/|\.venv/|venv/)

# Configuration for specific tools
default_language_version:
  python: python3.11
  node: "20.11.0"

# Global exclusions
exclude: |
  (?x)^(
    .*\.min\.(js|css)$|
    .*\.bundle\.(js|css)$|
    node_modules/.*|
    \.next/.*|
    dist/.*|
    build/.*|
    __pycache__/.*|
    \.git/.*|
    \.venv/.*|
    venv/.*|
    migrations/.*|
    \.mypy_cache/.*|
    \.pytest_cache/.*
  )$
