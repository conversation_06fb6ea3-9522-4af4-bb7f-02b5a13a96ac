# YAML linting configuration for Multi-Agent Real-Estate Contract Platform

extends: default

rules:
  # Line length
  line-length:
    max: 120
    level: warning

  # Indentation
  indentation:
    spaces: 2
    indent-sequences: true
    check-multi-line-strings: false

  # Comments
  comments:
    min-spaces-from-content: 1

  # Document start
  document-start:
    present: false

  # Truthy values
  truthy:
    allowed-values: ['true', 'false', 'yes', 'no']
    check-keys: false

  # Empty lines
  empty-lines:
    max: 2
    max-start: 0
    max-end: 1

  # Brackets
  brackets:
    min-spaces-inside: 0
    max-spaces-inside: 1

  # Braces
  braces:
    min-spaces-inside: 0
    max-spaces-inside: 1

# Ignore patterns
ignore: |
  .git/
  node_modules/
  .next/
  dist/
  build/
  __pycache__/
  .venv/
  venv/
  .mypy_cache/
  .pytest_cache/
