"""
Test package for the Multi-Agent Real-Estate Contract Platform backend.

This package contains all test modules organized by functionality:
- test_main: Tests for main application setup
- test_config: Tests for configuration management
- test_database: Tests for database operations
- test_auth: Tests for authentication (Phase 3)
- test_files: Tests for file operations (Phase 4)
- test_contracts: Tests for contract management (Phase 6)
- test_ai_agents: Tests for AI agent operations (Phase 10-11)
"""

# Test utilities and fixtures will be imported here
# from .conftest import *
# from .utils import *

__all__ = [
    # Test utilities will be exported here as they are implemented
]
