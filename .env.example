# Multi-Agent Real-Estate Contract Platform - Environment Variables
# Copy this file to .env and update with your actual values

# ===== DATABASE =====
DATABASE_URL=sqlite:///./database.db

# ===== REDIS =====
REDIS_URL=redis://localhost:6379/0

# ===== S3/MINIO STORAGE =====
S3_ENDPOINT=http://localhost:9000
S3_ACCESS_KEY=minioadmin
S3_SECRET_KEY=minioadmin123
S3_BUCKET_NAME=realestate-files
S3_REGION=us-east-1

# ===== JWT AUTHENTICATION =====
JWT_SECRET_KEY=your-secret-key-change-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# ===== FASTAPI =====
FASTAPI_ENV=development
FASTAPI_DEBUG=true
FASTAPI_RELOAD=true

# ===== CELERY =====
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# ===== AI/LLM CONFIGURATION =====
OPENROUTER_API_KEY=your-openrouter-api-key
OLLAMA_BASE_URL=http://localhost:11434

# ===== LOGGING =====
LOG_LEVEL=DEBUG

# ===== NEXT.JS FRONTEND =====
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_NAME="Real Estate Contract Platform"

# ===== E-SIGNATURE PROVIDER =====
# DOCUSIGN_CLIENT_ID=your-docusign-client-id
# DOCUSIGN_CLIENT_SECRET=your-docusign-client-secret
# DOCUSIGN_REDIRECT_URI=http://localhost:3000/auth/docusign/callback

# ===== EMAIL CONFIGURATION =====
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your-app-password

# ===== MONITORING =====
# SENTRY_DSN=your-sentry-dsn
