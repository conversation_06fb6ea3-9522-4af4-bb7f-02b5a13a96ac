{"config": {"configFile": "C:\\Users\\<USER>\\Desktop\\Contract App\\frontend\\playwright.config.ts", "rootDir": "C:/Users/<USER>/Desktop/Contract App/frontend/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/Desktop/Contract App/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/Desktop/Contract App/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/Contract App/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/Desktop/Contract App/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/Contract App/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/Desktop/Contract App/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/Contract App/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/Desktop/Contract App/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/Contract App/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/Desktop/Contract App/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.2", "workers": 8, "webServer": {"command": "npm run dev -- --port 3001", "url": "http://localhost:3001", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "admin.spec.ts", "file": "admin.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Admin Panel", "file": "admin.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should display admin panel interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-f59cc6b9e66bda762780", "file": "admin.spec.ts", "line": 17, "column": 7}, {"title": "should display admin tabs", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-e61923443498133ad1d4", "file": "admin.spec.ts", "line": 22, "column": 7}, {"title": "should display user management by default", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-958f87d54b651f8128ad", "file": "admin.spec.ts", "line": 29, "column": 7}, {"title": "should display user roles and status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-e66e20035608c16341b5", "file": "admin.spec.ts", "line": 42, "column": 7}, {"title": "should select user and show details", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-bfe69f6790d0321653b9", "file": "admin.spec.ts", "line": 51, "column": 7}, {"title": "should toggle user status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-f066547fa205f8d1766a", "file": "admin.spec.ts", "line": 67, "column": 7}, {"title": "should display templates tab", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-344075fd34c02c5c015f", "file": "admin.spec.ts", "line": 76, "column": 7}, {"title": "should display template information", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-de34f8264c6311ac6c86", "file": "admin.spec.ts", "line": 90, "column": 7}, {"title": "should change template status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-589cf7ace9683f14510c", "file": "admin.spec.ts", "line": 101, "column": 7}, {"title": "should display system configuration", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-f52cd711683db623919f", "file": "admin.spec.ts", "line": 114, "column": 7}, {"title": "should display audit logs", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-a0292caad894a88a4dba", "file": "admin.spec.ts", "line": 133, "column": 7}, {"title": "should display audit log details", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-e667abcb8d050ba6e274", "file": "admin.spec.ts", "line": 147, "column": 7}, {"title": "should save configuration changes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-3d61fa13efd6d3995423", "file": "admin.spec.ts", "line": 160, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-836bb13d6da903c79a99", "file": "admin.spec.ts", "line": 170, "column": 7}, {"title": "should display help button with admin context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-043790c36d2ac167cb15", "file": "admin.spec.ts", "line": 178, "column": 7}, {"title": "should show placeholder when no user selected", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-f2b3bd41af358a6a9484", "file": "admin.spec.ts", "line": 189, "column": 7}, {"title": "should maintain tab state during interactions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-4c14e639417de63c4496", "file": "admin.spec.ts", "line": 194, "column": 7}, {"title": "should display admin panel interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-2e92a226bc35e8f4933a", "file": "admin.spec.ts", "line": 17, "column": 7}, {"title": "should display admin tabs", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-fb82e2e520d793d8a58d", "file": "admin.spec.ts", "line": 22, "column": 7}, {"title": "should display user management by default", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-040650d96a37047874b8", "file": "admin.spec.ts", "line": 29, "column": 7}, {"title": "should display user roles and status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-5d178202c3200ca57fbc", "file": "admin.spec.ts", "line": 42, "column": 7}, {"title": "should select user and show details", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-cbeb8ec0cacc4e2b1ec5", "file": "admin.spec.ts", "line": 51, "column": 7}, {"title": "should toggle user status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-6b1d716d3ead96df3f26", "file": "admin.spec.ts", "line": 67, "column": 7}, {"title": "should display templates tab", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-7e430acddf5168d9b6bf", "file": "admin.spec.ts", "line": 76, "column": 7}, {"title": "should display template information", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-ed55a0072e58f920011c", "file": "admin.spec.ts", "line": 90, "column": 7}, {"title": "should change template status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-334bfb9978cc4a172086", "file": "admin.spec.ts", "line": 101, "column": 7}, {"title": "should display system configuration", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-ff77ca1f2c6df9c62194", "file": "admin.spec.ts", "line": 114, "column": 7}, {"title": "should display audit logs", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-22b9b6050edf26c757f3", "file": "admin.spec.ts", "line": 133, "column": 7}, {"title": "should display audit log details", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-da749e8a9ba952d5bbe3", "file": "admin.spec.ts", "line": 147, "column": 7}, {"title": "should save configuration changes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-e3d100d2efc37e6c1752", "file": "admin.spec.ts", "line": 160, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-550d890637075f71540a", "file": "admin.spec.ts", "line": 170, "column": 7}, {"title": "should display help button with admin context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-0b33c3f13d90fe43071d", "file": "admin.spec.ts", "line": 178, "column": 7}, {"title": "should show placeholder when no user selected", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-99356590ce88f4954cac", "file": "admin.spec.ts", "line": 189, "column": 7}, {"title": "should maintain tab state during interactions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-44a4c00bb2ea3557abc0", "file": "admin.spec.ts", "line": 194, "column": 7}, {"title": "should display admin panel interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-a4b0b736ab0eb616aa70", "file": "admin.spec.ts", "line": 17, "column": 7}, {"title": "should display admin tabs", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-0056c8e336f7dc1a13aa", "file": "admin.spec.ts", "line": 22, "column": 7}, {"title": "should display user management by default", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-c56f23815b5bc54da466", "file": "admin.spec.ts", "line": 29, "column": 7}, {"title": "should display user roles and status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-4d4655946e1d9255285f", "file": "admin.spec.ts", "line": 42, "column": 7}, {"title": "should select user and show details", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-5b25513e4594a14f0be5", "file": "admin.spec.ts", "line": 51, "column": 7}, {"title": "should toggle user status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-b4834d99251385611e97", "file": "admin.spec.ts", "line": 67, "column": 7}, {"title": "should display templates tab", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-c5c3c89ad56ba3b281d4", "file": "admin.spec.ts", "line": 76, "column": 7}, {"title": "should display template information", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-759f73c3c04a7ea0e155", "file": "admin.spec.ts", "line": 90, "column": 7}, {"title": "should change template status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-7c2defe6bac40ad0d611", "file": "admin.spec.ts", "line": 101, "column": 7}, {"title": "should display system configuration", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-de3242a4f87faaec6a17", "file": "admin.spec.ts", "line": 114, "column": 7}, {"title": "should display audit logs", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-60a399e230cc0ab70c37", "file": "admin.spec.ts", "line": 133, "column": 7}, {"title": "should display audit log details", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-47895767d808b0cbdafa", "file": "admin.spec.ts", "line": 147, "column": 7}, {"title": "should save configuration changes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-a179439728ef6c1e0e4c", "file": "admin.spec.ts", "line": 160, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-ea29525a43d78023602c", "file": "admin.spec.ts", "line": 170, "column": 7}, {"title": "should display help button with admin context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-fa9736e7b2cdc34ad431", "file": "admin.spec.ts", "line": 178, "column": 7}, {"title": "should show placeholder when no user selected", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-adb4d1905f12c4e22686", "file": "admin.spec.ts", "line": 189, "column": 7}, {"title": "should maintain tab state during interactions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-e1db79f751cafb9ea490", "file": "admin.spec.ts", "line": 194, "column": 7}, {"title": "should display admin panel interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-cf1c158b1fe6d0e8800c", "file": "admin.spec.ts", "line": 17, "column": 7}, {"title": "should display admin tabs", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-efd7971fbdd357338f31", "file": "admin.spec.ts", "line": 22, "column": 7}, {"title": "should display user management by default", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-a73cfb6272c33e4914a1", "file": "admin.spec.ts", "line": 29, "column": 7}, {"title": "should display user roles and status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-a739da3e0654e9d857f1", "file": "admin.spec.ts", "line": 42, "column": 7}, {"title": "should select user and show details", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-a5da8e92a6fe16d8aa07", "file": "admin.spec.ts", "line": 51, "column": 7}, {"title": "should toggle user status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-c018d96c7f8d0fb89b5e", "file": "admin.spec.ts", "line": 67, "column": 7}, {"title": "should display templates tab", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-af89f21fd35094b75c4d", "file": "admin.spec.ts", "line": 76, "column": 7}, {"title": "should display template information", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-f07373d919a241746494", "file": "admin.spec.ts", "line": 90, "column": 7}, {"title": "should change template status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-bf7eb07014f858b4faf7", "file": "admin.spec.ts", "line": 101, "column": 7}, {"title": "should display system configuration", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-c8404bed7ea0f133b93b", "file": "admin.spec.ts", "line": 114, "column": 7}, {"title": "should display audit logs", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-c5fd0c1713c520229f4c", "file": "admin.spec.ts", "line": 133, "column": 7}, {"title": "should display audit log details", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-318ed5687490b4ffc11f", "file": "admin.spec.ts", "line": 147, "column": 7}, {"title": "should save configuration changes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-e64d1da1eb33b075db7b", "file": "admin.spec.ts", "line": 160, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-a28369475feb8b6dc6b8", "file": "admin.spec.ts", "line": 170, "column": 7}, {"title": "should display help button with admin context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-cdcbe0b1708a5b91e346", "file": "admin.spec.ts", "line": 178, "column": 7}, {"title": "should show placeholder when no user selected", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-4eb2363fe8beafeeddcf", "file": "admin.spec.ts", "line": 189, "column": 7}, {"title": "should maintain tab state during interactions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-bd04261cc4df3a3ab288", "file": "admin.spec.ts", "line": 194, "column": 7}, {"title": "should display admin panel interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-a746c2bca5ebcd3d65bb", "file": "admin.spec.ts", "line": 17, "column": 7}, {"title": "should display admin tabs", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-42cbdd2b089ef603eca1", "file": "admin.spec.ts", "line": 22, "column": 7}, {"title": "should display user management by default", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-b785d2620e271b4259e8", "file": "admin.spec.ts", "line": 29, "column": 7}, {"title": "should display user roles and status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-7131264f1041d5e52d51", "file": "admin.spec.ts", "line": 42, "column": 7}, {"title": "should select user and show details", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-4798097ccdfc8fd6845b", "file": "admin.spec.ts", "line": 51, "column": 7}, {"title": "should toggle user status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-243ea4c4b4fc1bd7d5a4", "file": "admin.spec.ts", "line": 67, "column": 7}, {"title": "should display templates tab", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-4731e857408ef7319e03", "file": "admin.spec.ts", "line": 76, "column": 7}, {"title": "should display template information", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-5534372b74f83eeeadc2", "file": "admin.spec.ts", "line": 90, "column": 7}, {"title": "should change template status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-db0e02ee1abdd54c49a6", "file": "admin.spec.ts", "line": 101, "column": 7}, {"title": "should display system configuration", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-8a53f69d9ab9bac69d16", "file": "admin.spec.ts", "line": 114, "column": 7}, {"title": "should display audit logs", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-5c6af09fef1fa5ac1a64", "file": "admin.spec.ts", "line": 133, "column": 7}, {"title": "should display audit log details", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-47a219d51368956ce76f", "file": "admin.spec.ts", "line": 147, "column": 7}, {"title": "should save configuration changes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-f8a9d5a65438c8644ec9", "file": "admin.spec.ts", "line": 160, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-dda5b466dadfb017343c", "file": "admin.spec.ts", "line": 170, "column": 7}, {"title": "should display help button with admin context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-972ef197e42446971b01", "file": "admin.spec.ts", "line": 178, "column": 7}, {"title": "should show placeholder when no user selected", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-39887033f65f0671d7b8", "file": "admin.spec.ts", "line": 189, "column": 7}, {"title": "should maintain tab state during interactions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d4043a9dd1ab94463227-879aad6ad0c12daf7e26", "file": "admin.spec.ts", "line": 194, "column": 7}]}]}, {"title": "auth.spec.ts", "file": "auth.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Authentication Flow", "file": "auth.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should display homepage with get started button", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-d273fa79bfc2b7d6dbfe", "file": "auth.spec.ts", "line": 8, "column": 7}, {"title": "should navigate to login page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-d336617d28ee7955c0db", "file": "auth.spec.ts", "line": 15, "column": 7}, {"title": "should show validation errors for empty form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-17ab39f7a270a8bb33f9", "file": "auth.spec.ts", "line": 23, "column": 7}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-05954164880eaca3a0f9", "file": "auth.spec.ts", "line": 32, "column": 7}, {"title": "should successfully login with valid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-2825506ca7913f2dbd4e", "file": "auth.spec.ts", "line": 42, "column": 7}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-4d2eb13aa042aa0b2d41", "file": "auth.spec.ts", "line": 54, "column": 7}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-c0ce6ccc7b7e02794edb", "file": "auth.spec.ts", "line": 59, "column": 7}, {"title": "should maintain session across page refreshes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-55b690104650e6585b70", "file": "auth.spec.ts", "line": 73, "column": 7}, {"title": "should redirect authenticated user away from login page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-12dd60367ca4b013ebbb", "file": "auth.spec.ts", "line": 87, "column": 7}, {"title": "should display homepage with get started button", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-af2f45887f4ded61a3f8", "file": "auth.spec.ts", "line": 8, "column": 7}, {"title": "should navigate to login page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-3ab1474a9cc91bc6a870", "file": "auth.spec.ts", "line": 15, "column": 7}, {"title": "should show validation errors for empty form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-e1133d91878534308244", "file": "auth.spec.ts", "line": 23, "column": 7}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-5579b1115e5e26c1ba43", "file": "auth.spec.ts", "line": 32, "column": 7}, {"title": "should successfully login with valid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-be4a0cc21a9f71ab9a66", "file": "auth.spec.ts", "line": 42, "column": 7}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-4329ca7f3e8c21f77534", "file": "auth.spec.ts", "line": 54, "column": 7}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-4410c42745b7a4d89d03", "file": "auth.spec.ts", "line": 59, "column": 7}, {"title": "should maintain session across page refreshes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-7c9be9efd0eed256bf5a", "file": "auth.spec.ts", "line": 73, "column": 7}, {"title": "should redirect authenticated user away from login page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-9be32ff08b8867ad75c5", "file": "auth.spec.ts", "line": 87, "column": 7}, {"title": "should display homepage with get started button", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-ab12f2cc685d657138d6", "file": "auth.spec.ts", "line": 8, "column": 7}, {"title": "should navigate to login page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-d580e06f853a527409c1", "file": "auth.spec.ts", "line": 15, "column": 7}, {"title": "should show validation errors for empty form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-bb4afae2d711af588f60", "file": "auth.spec.ts", "line": 23, "column": 7}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-4e575a42f2e3a5f20db2", "file": "auth.spec.ts", "line": 32, "column": 7}, {"title": "should successfully login with valid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-61a32ad5b38d503c68f7", "file": "auth.spec.ts", "line": 42, "column": 7}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-21fd3dcd248dab5b8131", "file": "auth.spec.ts", "line": 54, "column": 7}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-1f08bff9df919d288ae6", "file": "auth.spec.ts", "line": 59, "column": 7}, {"title": "should maintain session across page refreshes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-8c9d69f0f520361c8dcb", "file": "auth.spec.ts", "line": 73, "column": 7}, {"title": "should redirect authenticated user away from login page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-66530fc1d59d2ab92e49", "file": "auth.spec.ts", "line": 87, "column": 7}, {"title": "should display homepage with get started button", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-140305ed34a4eb3fba98", "file": "auth.spec.ts", "line": 8, "column": 7}, {"title": "should navigate to login page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-f53af8448c525e773f38", "file": "auth.spec.ts", "line": 15, "column": 7}, {"title": "should show validation errors for empty form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-465243df483e808a6107", "file": "auth.spec.ts", "line": 23, "column": 7}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-12287ecea93857666643", "file": "auth.spec.ts", "line": 32, "column": 7}, {"title": "should successfully login with valid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-db2e2a1ad85c3d9ef330", "file": "auth.spec.ts", "line": 42, "column": 7}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-32453ae2bfafae33ef23", "file": "auth.spec.ts", "line": 54, "column": 7}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-8a3b860ad917d672a1c0", "file": "auth.spec.ts", "line": 59, "column": 7}, {"title": "should maintain session across page refreshes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-2b0b71cfa356118c9c52", "file": "auth.spec.ts", "line": 73, "column": 7}, {"title": "should redirect authenticated user away from login page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-a428339fe9e02c5552d1", "file": "auth.spec.ts", "line": 87, "column": 7}, {"title": "should display homepage with get started button", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-8b98c50cfca4f9e5ae6c", "file": "auth.spec.ts", "line": 8, "column": 7}, {"title": "should navigate to login page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-80c0498402e0f23f6a55", "file": "auth.spec.ts", "line": 15, "column": 7}, {"title": "should show validation errors for empty form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-bd5f9991c92ddd1490ed", "file": "auth.spec.ts", "line": 23, "column": 7}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-0c3a71e4f2facbc6e673", "file": "auth.spec.ts", "line": 32, "column": 7}, {"title": "should successfully login with valid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-e883f91b94093e126c60", "file": "auth.spec.ts", "line": 42, "column": 7}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-91f7170e74c610e935e3", "file": "auth.spec.ts", "line": 54, "column": 7}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-5e89033e25aea64867f4", "file": "auth.spec.ts", "line": 59, "column": 7}, {"title": "should maintain session across page refreshes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-14bea0b4df439ad9d58a", "file": "auth.spec.ts", "line": 73, "column": 7}, {"title": "should redirect authenticated user away from login page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-ee01e1e4bf370312b927", "file": "auth.spec.ts", "line": 87, "column": 7}]}]}, {"title": "contracts.spec.ts", "file": "contracts.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Contract Generator", "file": "contracts.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should display contract generator interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-6757f11267eb702f71a5", "file": "contracts.spec.ts", "line": 17, "column": 7}, {"title": "should display template selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-1666d261e0a8107c0e90", "file": "contracts.spec.ts", "line": 27, "column": 7}, {"title": "should select template and navigate to variables tab", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-6c9e756056d00b507f10", "file": "contracts.spec.ts", "line": 38, "column": 7}, {"title": "should display form fields for selected template", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-db234e22bd29ca7459cb", "file": "contracts.spec.ts", "line": 48, "column": 7}, {"title": "should fill form and generate contract", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-d821c1a14b2e2c9e41ae", "file": "contracts.spec.ts", "line": 62, "column": 7}, {"title": "should validate required fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-a97193b2174a88c689df", "file": "contracts.spec.ts", "line": 86, "column": 7}, {"title": "should display contract preview", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-fedb32e9e8a1434f49b7", "file": "contracts.spec.ts", "line": 110, "column": 7}, {"title": "should allow editing variables from preview", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-4ff00cae16594f847359", "file": "contracts.spec.ts", "line": 133, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-9412ef366b65791965fb", "file": "contracts.spec.ts", "line": 153, "column": 7}, {"title": "should display help button with contract context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-c5c10d9fca950820c1f6", "file": "contracts.spec.ts", "line": 161, "column": 7}, {"title": "should display contract generator interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-4ecc71ac3ed9e5eb21e6", "file": "contracts.spec.ts", "line": 17, "column": 7}, {"title": "should display template selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-a24123900475ba4d3800", "file": "contracts.spec.ts", "line": 27, "column": 7}, {"title": "should select template and navigate to variables tab", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-dbda774fd7b5c8cd4451", "file": "contracts.spec.ts", "line": 38, "column": 7}, {"title": "should display form fields for selected template", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-962e27300ec255ec080d", "file": "contracts.spec.ts", "line": 48, "column": 7}, {"title": "should fill form and generate contract", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-77db34bbfcde6cf13d7b", "file": "contracts.spec.ts", "line": 62, "column": 7}, {"title": "should validate required fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-791e8d16ede79c7350c1", "file": "contracts.spec.ts", "line": 86, "column": 7}, {"title": "should display contract preview", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-7fb6d9d6a423872101eb", "file": "contracts.spec.ts", "line": 110, "column": 7}, {"title": "should allow editing variables from preview", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-6057be938f42b496c137", "file": "contracts.spec.ts", "line": 133, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-13ebe84dd7d86c19d772", "file": "contracts.spec.ts", "line": 153, "column": 7}, {"title": "should display help button with contract context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-15668cb6143557754d23", "file": "contracts.spec.ts", "line": 161, "column": 7}, {"title": "should display contract generator interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-1ecb9592d84cabdded9b", "file": "contracts.spec.ts", "line": 17, "column": 7}, {"title": "should display template selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-3e6a37a4b2a741bed9a0", "file": "contracts.spec.ts", "line": 27, "column": 7}, {"title": "should select template and navigate to variables tab", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-c7ab6275a796deaebc20", "file": "contracts.spec.ts", "line": 38, "column": 7}, {"title": "should display form fields for selected template", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-aa1a354e74d44df3c296", "file": "contracts.spec.ts", "line": 48, "column": 7}, {"title": "should fill form and generate contract", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-ed1d36dab65d4d16cadc", "file": "contracts.spec.ts", "line": 62, "column": 7}, {"title": "should validate required fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-f28cda8a786ec9070373", "file": "contracts.spec.ts", "line": 86, "column": 7}, {"title": "should display contract preview", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-55670fcd2ae2d071b3d4", "file": "contracts.spec.ts", "line": 110, "column": 7}, {"title": "should allow editing variables from preview", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-143df8ec9bd208b91b65", "file": "contracts.spec.ts", "line": 133, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-61bbbdfc146154dc7382", "file": "contracts.spec.ts", "line": 153, "column": 7}, {"title": "should display help button with contract context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-9229ec6696209dbac6fd", "file": "contracts.spec.ts", "line": 161, "column": 7}, {"title": "should display contract generator interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-c705a02acd4356dbc1a0", "file": "contracts.spec.ts", "line": 17, "column": 7}, {"title": "should display template selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-53fec535b10d5d99010b", "file": "contracts.spec.ts", "line": 27, "column": 7}, {"title": "should select template and navigate to variables tab", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-04dbb1cc2c06746f16ee", "file": "contracts.spec.ts", "line": 38, "column": 7}, {"title": "should display form fields for selected template", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-5c6e6d00f2055f7592f2", "file": "contracts.spec.ts", "line": 48, "column": 7}, {"title": "should fill form and generate contract", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-3a39083ada7bbef2781e", "file": "contracts.spec.ts", "line": 62, "column": 7}, {"title": "should validate required fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-d3eb712698ecf6e2c1f9", "file": "contracts.spec.ts", "line": 86, "column": 7}, {"title": "should display contract preview", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-80006df89af2de0c0246", "file": "contracts.spec.ts", "line": 110, "column": 7}, {"title": "should allow editing variables from preview", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-9f4f90c0b243d662b46e", "file": "contracts.spec.ts", "line": 133, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-369fd5207b77d75e0e96", "file": "contracts.spec.ts", "line": 153, "column": 7}, {"title": "should display help button with contract context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-960cef81ee2998cf89a6", "file": "contracts.spec.ts", "line": 161, "column": 7}, {"title": "should display contract generator interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-e0388c3a0d3047f184cc", "file": "contracts.spec.ts", "line": 17, "column": 7}, {"title": "should display template selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-f13c265b5437e994fc2f", "file": "contracts.spec.ts", "line": 27, "column": 7}, {"title": "should select template and navigate to variables tab", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-c274fc6f2134df8e7359", "file": "contracts.spec.ts", "line": 38, "column": 7}, {"title": "should display form fields for selected template", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-2aee112873944e2c3c85", "file": "contracts.spec.ts", "line": 48, "column": 7}, {"title": "should fill form and generate contract", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-db01e849525a24b7fbd7", "file": "contracts.spec.ts", "line": 62, "column": 7}, {"title": "should validate required fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-d51aba8052faf22dd5b4", "file": "contracts.spec.ts", "line": 86, "column": 7}, {"title": "should display contract preview", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-07bc53ac8f7cda3fd5b3", "file": "contracts.spec.ts", "line": 110, "column": 7}, {"title": "should allow editing variables from preview", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-4ad5a7c54f9ca39567b1", "file": "contracts.spec.ts", "line": 133, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-08f15d89ad1e742f37ab", "file": "contracts.spec.ts", "line": 153, "column": 7}, {"title": "should display help button with contract context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "94d2a11b1fc1ed31b81d-05e53ab034c59c72082c", "file": "contracts.spec.ts", "line": 161, "column": 7}]}]}, {"title": "dashboard.spec.ts", "file": "dashboard.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Dashboard", "file": "dashboard.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should display dashboard with all widgets", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-109229335b67a39751d4", "file": "dashboard.spec.ts", "line": 13, "column": 7}, {"title": "should display quick action cards", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-1f96629b08054b065594", "file": "dashboard.spec.ts", "line": 30, "column": 7}, {"title": "should navigate to documents page from quick action", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-c04260d23e125ecd8208", "file": "dashboard.spec.ts", "line": 41, "column": 7}, {"title": "should navigate to contracts page from quick action", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-1f6879363b261b19ae1c", "file": "dashboard.spec.ts", "line": 47, "column": 7}, {"title": "should navigate to signatures page from quick action", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-e7eed541c1fcafc60b58", "file": "dashboard.spec.ts", "line": 53, "column": 7}, {"title": "should display navigation with all menu items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-24d3634fe32a7fd75a49", "file": "dashboard.spec.ts", "line": 59, "column": 7}, {"title": "should navigate between pages using navigation menu", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-ee9739fec7e1d588a841", "file": "dashboard.spec.ts", "line": 67, "column": 7}, {"title": "should display user information in header", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-5ad9563f04d1a38c2d65", "file": "dashboard.spec.ts", "line": 89, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-7036cde7cb3054f87671", "file": "dashboard.spec.ts", "line": 95, "column": 7}, {"title": "should display help button", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-1d6f679e89c24ec9d914", "file": "dashboard.spec.ts", "line": 110, "column": 7}, {"title": "should display dashboard with all widgets", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-ed9ae7deaf4d01f6b44b", "file": "dashboard.spec.ts", "line": 13, "column": 7}, {"title": "should display quick action cards", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-5dd0815614a5dbcbe283", "file": "dashboard.spec.ts", "line": 30, "column": 7}, {"title": "should navigate to documents page from quick action", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-b3cc105dc58972e3e441", "file": "dashboard.spec.ts", "line": 41, "column": 7}, {"title": "should navigate to contracts page from quick action", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-65e3a48f1b0445f50f50", "file": "dashboard.spec.ts", "line": 47, "column": 7}, {"title": "should navigate to signatures page from quick action", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-a3ddbf4c67d39b7b7bd6", "file": "dashboard.spec.ts", "line": 53, "column": 7}, {"title": "should display navigation with all menu items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-7bf16d8c644d7c76568f", "file": "dashboard.spec.ts", "line": 59, "column": 7}, {"title": "should navigate between pages using navigation menu", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-1c3131ffe5ce723aac3d", "file": "dashboard.spec.ts", "line": 67, "column": 7}, {"title": "should display user information in header", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-eab61cabbb42ee792aa1", "file": "dashboard.spec.ts", "line": 89, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-4f53e95f095180c5fef8", "file": "dashboard.spec.ts", "line": 95, "column": 7}, {"title": "should display help button", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-aa5839269bb6ffb36a46", "file": "dashboard.spec.ts", "line": 110, "column": 7}, {"title": "should display dashboard with all widgets", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-cfb3c88cc8ff8bf8ebcf", "file": "dashboard.spec.ts", "line": 13, "column": 7}, {"title": "should display quick action cards", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-f45e769ab9d53621f1de", "file": "dashboard.spec.ts", "line": 30, "column": 7}, {"title": "should navigate to documents page from quick action", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-b21918c0839d38023fad", "file": "dashboard.spec.ts", "line": 41, "column": 7}, {"title": "should navigate to contracts page from quick action", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-95acbc08396c284a5928", "file": "dashboard.spec.ts", "line": 47, "column": 7}, {"title": "should navigate to signatures page from quick action", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-4b88742e878610b463d8", "file": "dashboard.spec.ts", "line": 53, "column": 7}, {"title": "should display navigation with all menu items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-6f0e5bc99538b31bd782", "file": "dashboard.spec.ts", "line": 59, "column": 7}, {"title": "should navigate between pages using navigation menu", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-aa714f3260af30519f91", "file": "dashboard.spec.ts", "line": 67, "column": 7}, {"title": "should display user information in header", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-20122aafacef741594c6", "file": "dashboard.spec.ts", "line": 89, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-889de46f8c8cbfc7edd4", "file": "dashboard.spec.ts", "line": 95, "column": 7}, {"title": "should display help button", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-26514582b99d4e6bbea9", "file": "dashboard.spec.ts", "line": 110, "column": 7}, {"title": "should display dashboard with all widgets", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-1d2d883031bfaceb6ead", "file": "dashboard.spec.ts", "line": 13, "column": 7}, {"title": "should display quick action cards", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-d16c0fa4144a878a1496", "file": "dashboard.spec.ts", "line": 30, "column": 7}, {"title": "should navigate to documents page from quick action", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-54e2ab87b5121777b28f", "file": "dashboard.spec.ts", "line": 41, "column": 7}, {"title": "should navigate to contracts page from quick action", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-c829dcb545398fb28c84", "file": "dashboard.spec.ts", "line": 47, "column": 7}, {"title": "should navigate to signatures page from quick action", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-38a5bc3e60fecfbe06e0", "file": "dashboard.spec.ts", "line": 53, "column": 7}, {"title": "should display navigation with all menu items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-dd72e0ca38fd4c2e7a3b", "file": "dashboard.spec.ts", "line": 59, "column": 7}, {"title": "should navigate between pages using navigation menu", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-4ef2d84b67e5b7c853d9", "file": "dashboard.spec.ts", "line": 67, "column": 7}, {"title": "should display user information in header", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-0ed5b84474e8677b97d0", "file": "dashboard.spec.ts", "line": 89, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-4dddd49deeffc5d23d49", "file": "dashboard.spec.ts", "line": 95, "column": 7}, {"title": "should display help button", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-ddfc591cd20e6793c6e2", "file": "dashboard.spec.ts", "line": 110, "column": 7}, {"title": "should display dashboard with all widgets", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-6cca78b683a9266c6b75", "file": "dashboard.spec.ts", "line": 13, "column": 7}, {"title": "should display quick action cards", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-c051647b337dfd1fae39", "file": "dashboard.spec.ts", "line": 30, "column": 7}, {"title": "should navigate to documents page from quick action", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-b360880f6c1b82d271ab", "file": "dashboard.spec.ts", "line": 41, "column": 7}, {"title": "should navigate to contracts page from quick action", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-ac228e1b549454279b8b", "file": "dashboard.spec.ts", "line": 47, "column": 7}, {"title": "should navigate to signatures page from quick action", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-8b33f46f36001a12e2e3", "file": "dashboard.spec.ts", "line": 53, "column": 7}, {"title": "should display navigation with all menu items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-562f11874c983c23df46", "file": "dashboard.spec.ts", "line": 59, "column": 7}, {"title": "should navigate between pages using navigation menu", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-9a34f3cfb20c8e94c423", "file": "dashboard.spec.ts", "line": 67, "column": 7}, {"title": "should display user information in header", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-74855df0ca834928749e", "file": "dashboard.spec.ts", "line": 89, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-867faf5a5d3e2573bdfc", "file": "dashboard.spec.ts", "line": 95, "column": 7}, {"title": "should display help button", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-7aebb2a54779beb27267", "file": "dashboard.spec.ts", "line": 110, "column": 7}]}]}, {"title": "documents.spec.ts", "file": "documents.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Document Intake", "file": "documents.spec.ts", "line": 4, "column": 6, "specs": [{"title": "should display document intake interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-b3baef96f56213ce32b3", "file": "documents.spec.ts", "line": 18, "column": 7}, {"title": "should show drag and drop interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-49747289846dcaf21190", "file": "documents.spec.ts", "line": 28, "column": 7}, {"title": "should handle file upload simulation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-62cec68d5ff3c493a5db", "file": "documents.spec.ts", "line": 37, "column": 7}, {"title": "should display uploaded files section when files are present", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-413621fa45d1b7b1bee0", "file": "documents.spec.ts", "line": 49, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-2ab3dd055296e9fcef71", "file": "documents.spec.ts", "line": 61, "column": 7}, {"title": "should display help button with document context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-a6825b905a8d984dc531", "file": "documents.spec.ts", "line": 69, "column": 7}, {"title": "should navigate back to dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-405c4485bbf963e4b4ef", "file": "documents.spec.ts", "line": 80, "column": 7}, {"title": "should maintain authentication state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-46f5bd4e518675b213f0", "file": "documents.spec.ts", "line": 85, "column": 7}, {"title": "should display document intake interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-7d84d0aea4c49ad934a5", "file": "documents.spec.ts", "line": 18, "column": 7}, {"title": "should show drag and drop interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-0c73013cc0516981218e", "file": "documents.spec.ts", "line": 28, "column": 7}, {"title": "should handle file upload simulation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-6e4ae81e81603e4db204", "file": "documents.spec.ts", "line": 37, "column": 7}, {"title": "should display uploaded files section when files are present", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-32ac3784ddbc422ce633", "file": "documents.spec.ts", "line": 49, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-b2633968136cec347863", "file": "documents.spec.ts", "line": 61, "column": 7}, {"title": "should display help button with document context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-2af68007e35a314575fc", "file": "documents.spec.ts", "line": 69, "column": 7}, {"title": "should navigate back to dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-6d0b04333024f5ce9e00", "file": "documents.spec.ts", "line": 80, "column": 7}, {"title": "should maintain authentication state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-9440f02453ca9f0ebb56", "file": "documents.spec.ts", "line": 85, "column": 7}, {"title": "should display document intake interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-591f75d8afba5f5d926a", "file": "documents.spec.ts", "line": 18, "column": 7}, {"title": "should show drag and drop interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-fb6ab78236c211451093", "file": "documents.spec.ts", "line": 28, "column": 7}, {"title": "should handle file upload simulation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-20405081fdd9ed97e0c2", "file": "documents.spec.ts", "line": 37, "column": 7}, {"title": "should display uploaded files section when files are present", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-fe55833313b430e21b06", "file": "documents.spec.ts", "line": 49, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-9f7c361f35ebac2497a1", "file": "documents.spec.ts", "line": 61, "column": 7}, {"title": "should display help button with document context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-1c90b93ae8ad8165a97b", "file": "documents.spec.ts", "line": 69, "column": 7}, {"title": "should navigate back to dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-8f90a1e614b639e21260", "file": "documents.spec.ts", "line": 80, "column": 7}, {"title": "should maintain authentication state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-dceecbe24fb40ec99c59", "file": "documents.spec.ts", "line": 85, "column": 7}, {"title": "should display document intake interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-b873e33cd4ec761d0956", "file": "documents.spec.ts", "line": 18, "column": 7}, {"title": "should show drag and drop interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-2e8cf2ebf7b218ad3734", "file": "documents.spec.ts", "line": 28, "column": 7}, {"title": "should handle file upload simulation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-f5cfe60ab7039178decf", "file": "documents.spec.ts", "line": 37, "column": 7}, {"title": "should display uploaded files section when files are present", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-f3bdc2bc6662ebd7a891", "file": "documents.spec.ts", "line": 49, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-1dc1c0336cb7553b2d25", "file": "documents.spec.ts", "line": 61, "column": 7}, {"title": "should display help button with document context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-3c7be58a6bf0fefb9732", "file": "documents.spec.ts", "line": 69, "column": 7}, {"title": "should navigate back to dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-d0c091f6ad8ca27bcf9a", "file": "documents.spec.ts", "line": 80, "column": 7}, {"title": "should maintain authentication state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-45d16d00c6fbecf24a84", "file": "documents.spec.ts", "line": 85, "column": 7}, {"title": "should display document intake interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-9e5f9195ae1326fa8148", "file": "documents.spec.ts", "line": 18, "column": 7}, {"title": "should show drag and drop interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-c7e684fb2aec08a37762", "file": "documents.spec.ts", "line": 28, "column": 7}, {"title": "should handle file upload simulation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-c9d117efae7e7fdfc6dd", "file": "documents.spec.ts", "line": 37, "column": 7}, {"title": "should display uploaded files section when files are present", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-253b9f87886e45445be0", "file": "documents.spec.ts", "line": 49, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-25c94b83f41fc10bddc1", "file": "documents.spec.ts", "line": 61, "column": 7}, {"title": "should display help button with document context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-048891784097ecebfff8", "file": "documents.spec.ts", "line": 69, "column": 7}, {"title": "should navigate back to dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-d43a09ae699a9a8727e2", "file": "documents.spec.ts", "line": 80, "column": 7}, {"title": "should maintain authentication state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "9111b41e0f82b685c9ea-e91e81f2525012a21a47", "file": "documents.spec.ts", "line": 85, "column": 7}]}]}, {"title": "review.spec.ts", "file": "review.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Contract Review", "file": "review.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should display review interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-30566e41ecf3b77a7611", "file": "review.spec.ts", "line": 16, "column": 7}, {"title": "should display review tabs", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-67de678c218d2e689fdc", "file": "review.spec.ts", "line": 24, "column": 7}, {"title": "should display current version information", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-6e203414611c762d58e4", "file": "review.spec.ts", "line": 30, "column": 7}, {"title": "should display contract with changes highlighted", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-37ee3e6c95df89be66de", "file": "review.spec.ts", "line": 36, "column": 7}, {"title": "should display line numbers", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-3e494e1fe0a36cf76ddc", "file": "review.spec.ts", "line": 47, "column": 7}, {"title": "should display comments on lines", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-659d8b6600f76e0d0898", "file": "review.spec.ts", "line": 53, "column": 7}, {"title": "should display comment replies", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-7e4497d51869529ab706", "file": "review.spec.ts", "line": 61, "column": 7}, {"title": "should allow selecting lines for comments", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-dc0b927a36c1fd594556", "file": "review.spec.ts", "line": 67, "column": 7}, {"title": "should add new comments", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-2b6f5dbd6e446f9ff888", "file": "review.spec.ts", "line": 75, "column": 7}, {"title": "should clear line selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-0a69627bcf59d86b7e0d", "file": "review.spec.ts", "line": 88, "column": 7}, {"title": "should display version history", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-62485f8081abcefd28c0", "file": "review.spec.ts", "line": 98, "column": 7}, {"title": "should switch between versions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-d9b6d83f6b1eeb5b5d6d", "file": "review.spec.ts", "line": 109, "column": 7}, {"title": "should display all comments tab", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-31016643ac3d408d6fe0", "file": "review.spec.ts", "line": 121, "column": 7}, {"title": "should display review summary", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-95ad2f6e045304ca9fb3", "file": "review.spec.ts", "line": 134, "column": 7}, {"title": "should approve version with keyboard shortcut", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-29723ef63218700b0ab9", "file": "review.spec.ts", "line": 143, "column": 7}, {"title": "should request changes with keyboard shortcut", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-043133146ddc783f95bf", "file": "review.spec.ts", "line": 152, "column": 7}, {"title": "should approve version with button", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-ce92e2dd11d9e39342f1", "file": "review.spec.ts", "line": 161, "column": 7}, {"title": "should request changes with button", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-59db8ab275740f4e7df5", "file": "review.spec.ts", "line": 169, "column": 7}, {"title": "should display keyboard shortcut indicators on buttons", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-3b63f7baeff3d8310552", "file": "review.spec.ts", "line": 177, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-43cb5b078bf9d9d84747", "file": "review.spec.ts", "line": 183, "column": 7}, {"title": "should display help button with review context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-6ec51a30845d53bd76f4", "file": "review.spec.ts", "line": 191, "column": 7}, {"title": "should display review interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-fc7f54c259e8878cc6a7", "file": "review.spec.ts", "line": 16, "column": 7}, {"title": "should display review tabs", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-4f92f629e266e62e3d7a", "file": "review.spec.ts", "line": 24, "column": 7}, {"title": "should display current version information", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-e88756f9298daedf6f9d", "file": "review.spec.ts", "line": 30, "column": 7}, {"title": "should display contract with changes highlighted", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-d3c44fa9d1ff849e5fcb", "file": "review.spec.ts", "line": 36, "column": 7}, {"title": "should display line numbers", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-fd3acecfb7990de58ea9", "file": "review.spec.ts", "line": 47, "column": 7}, {"title": "should display comments on lines", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-c84b1857e9d952d8c671", "file": "review.spec.ts", "line": 53, "column": 7}, {"title": "should display comment replies", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-692be495d31380af48ac", "file": "review.spec.ts", "line": 61, "column": 7}, {"title": "should allow selecting lines for comments", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-d9d0d40f4c28cbe02afc", "file": "review.spec.ts", "line": 67, "column": 7}, {"title": "should add new comments", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-aedbeeea14164727126c", "file": "review.spec.ts", "line": 75, "column": 7}, {"title": "should clear line selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-c49d78ffa28a861a4cef", "file": "review.spec.ts", "line": 88, "column": 7}, {"title": "should display version history", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-24f6881a1a35416819ff", "file": "review.spec.ts", "line": 98, "column": 7}, {"title": "should switch between versions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-377c8189756828295c6f", "file": "review.spec.ts", "line": 109, "column": 7}, {"title": "should display all comments tab", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-22bb30a3ef56af1ada6b", "file": "review.spec.ts", "line": 121, "column": 7}, {"title": "should display review summary", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-7737051f44a044c605a8", "file": "review.spec.ts", "line": 134, "column": 7}, {"title": "should approve version with keyboard shortcut", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-ebbfe874a24b588229c0", "file": "review.spec.ts", "line": 143, "column": 7}, {"title": "should request changes with keyboard shortcut", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-41dd9fce08fd9d52407f", "file": "review.spec.ts", "line": 152, "column": 7}, {"title": "should approve version with button", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-be9d0a04f1619bfce982", "file": "review.spec.ts", "line": 161, "column": 7}, {"title": "should request changes with button", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-02e1117d4933f30fb65c", "file": "review.spec.ts", "line": 169, "column": 7}, {"title": "should display keyboard shortcut indicators on buttons", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-6dc3c06d256d57f4d7be", "file": "review.spec.ts", "line": 177, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-f421cb97a2bde5763de0", "file": "review.spec.ts", "line": 183, "column": 7}, {"title": "should display help button with review context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-288129e5f9cca1f7dc58", "file": "review.spec.ts", "line": 191, "column": 7}, {"title": "should display review interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-bd5128936f504d90af54", "file": "review.spec.ts", "line": 16, "column": 7}, {"title": "should display review tabs", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-40922d650e61eb00855c", "file": "review.spec.ts", "line": 24, "column": 7}, {"title": "should display current version information", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-bdf5fd6ae7a4bb26131b", "file": "review.spec.ts", "line": 30, "column": 7}, {"title": "should display contract with changes highlighted", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-64973c7191ca3f9e2825", "file": "review.spec.ts", "line": 36, "column": 7}, {"title": "should display line numbers", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-b0ef76e43747d4aaeb00", "file": "review.spec.ts", "line": 47, "column": 7}, {"title": "should display comments on lines", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-bf0ac741cbd5dc28cbc9", "file": "review.spec.ts", "line": 53, "column": 7}, {"title": "should display comment replies", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-ce8a56e5ea7eb6a0c06a", "file": "review.spec.ts", "line": 61, "column": 7}, {"title": "should allow selecting lines for comments", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-bc5fa7aa3ccb1add4e2a", "file": "review.spec.ts", "line": 67, "column": 7}, {"title": "should add new comments", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-59ac904ec8c03df85a7c", "file": "review.spec.ts", "line": 75, "column": 7}, {"title": "should clear line selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-3617f3ef9369fece7185", "file": "review.spec.ts", "line": 88, "column": 7}, {"title": "should display version history", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-5d08a6df082d07f66a6d", "file": "review.spec.ts", "line": 98, "column": 7}, {"title": "should switch between versions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-b2020518a33361fb30d6", "file": "review.spec.ts", "line": 109, "column": 7}, {"title": "should display all comments tab", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-7774dd0934b63307b021", "file": "review.spec.ts", "line": 121, "column": 7}, {"title": "should display review summary", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-47273267581ba767000c", "file": "review.spec.ts", "line": 134, "column": 7}, {"title": "should approve version with keyboard shortcut", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-5c32661a1da0eea25dd4", "file": "review.spec.ts", "line": 143, "column": 7}, {"title": "should request changes with keyboard shortcut", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-d699a2872671ac58ff15", "file": "review.spec.ts", "line": 152, "column": 7}, {"title": "should approve version with button", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-330c447baa0ff6309c86", "file": "review.spec.ts", "line": 161, "column": 7}, {"title": "should request changes with button", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-20ef635eba7af2d0ea90", "file": "review.spec.ts", "line": 169, "column": 7}, {"title": "should display keyboard shortcut indicators on buttons", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-5a0b57a37b8a69b5a013", "file": "review.spec.ts", "line": 177, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-47c3ec7a9899dc1f3590", "file": "review.spec.ts", "line": 183, "column": 7}, {"title": "should display help button with review context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-58cf195feeeffae7382c", "file": "review.spec.ts", "line": 191, "column": 7}, {"title": "should display review interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-2eef0ffd34f0d8974523", "file": "review.spec.ts", "line": 16, "column": 7}, {"title": "should display review tabs", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-e8916ecaa1f649ac4871", "file": "review.spec.ts", "line": 24, "column": 7}, {"title": "should display current version information", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-19d939db549c4c1dda1f", "file": "review.spec.ts", "line": 30, "column": 7}, {"title": "should display contract with changes highlighted", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-72214905c63cd1145437", "file": "review.spec.ts", "line": 36, "column": 7}, {"title": "should display line numbers", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-d18ae96da6d80a9ac995", "file": "review.spec.ts", "line": 47, "column": 7}, {"title": "should display comments on lines", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-a3c51e1028f5233ce40d", "file": "review.spec.ts", "line": 53, "column": 7}, {"title": "should display comment replies", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-6b4fc552119def64be1b", "file": "review.spec.ts", "line": 61, "column": 7}, {"title": "should allow selecting lines for comments", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-56fa6be6340dbf171541", "file": "review.spec.ts", "line": 67, "column": 7}, {"title": "should add new comments", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-0f748cf0057d07aa4fb7", "file": "review.spec.ts", "line": 75, "column": 7}, {"title": "should clear line selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-90b51<PERSON>dace8b9815ffa", "file": "review.spec.ts", "line": 88, "column": 7}, {"title": "should display version history", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-96354394eed97c78e8ff", "file": "review.spec.ts", "line": 98, "column": 7}, {"title": "should switch between versions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-0b23ffa40409bb64d86a", "file": "review.spec.ts", "line": 109, "column": 7}, {"title": "should display all comments tab", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-9d3174b053130c9f2309", "file": "review.spec.ts", "line": 121, "column": 7}, {"title": "should display review summary", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-cf6f7b8795f16e5fa4ed", "file": "review.spec.ts", "line": 134, "column": 7}, {"title": "should approve version with keyboard shortcut", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-4819a877f68742409bbd", "file": "review.spec.ts", "line": 143, "column": 7}, {"title": "should request changes with keyboard shortcut", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-62ebac26d51cd57d9f6c", "file": "review.spec.ts", "line": 152, "column": 7}, {"title": "should approve version with button", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-407171c4c3ee2c572066", "file": "review.spec.ts", "line": 161, "column": 7}, {"title": "should request changes with button", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-58fa7b0410e8ec6e381b", "file": "review.spec.ts", "line": 169, "column": 7}, {"title": "should display keyboard shortcut indicators on buttons", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-3482b56e4f2942bf7543", "file": "review.spec.ts", "line": 177, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-2380d8aab533f80de769", "file": "review.spec.ts", "line": 183, "column": 7}, {"title": "should display help button with review context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-4587f4841fa0323fed9b", "file": "review.spec.ts", "line": 191, "column": 7}, {"title": "should display review interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-aa142a365ee1e25d7fa7", "file": "review.spec.ts", "line": 16, "column": 7}, {"title": "should display review tabs", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-aa09763d3914d264f394", "file": "review.spec.ts", "line": 24, "column": 7}, {"title": "should display current version information", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-eb0126ec04524266357f", "file": "review.spec.ts", "line": 30, "column": 7}, {"title": "should display contract with changes highlighted", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-d3f4eac05752c5e3afb6", "file": "review.spec.ts", "line": 36, "column": 7}, {"title": "should display line numbers", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-1673f56514f262fd8091", "file": "review.spec.ts", "line": 47, "column": 7}, {"title": "should display comments on lines", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-7f3a6942d160c4569cf6", "file": "review.spec.ts", "line": 53, "column": 7}, {"title": "should display comment replies", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-e4d9145c3e53104cbedf", "file": "review.spec.ts", "line": 61, "column": 7}, {"title": "should allow selecting lines for comments", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-13036cf5bb5bff79db7e", "file": "review.spec.ts", "line": 67, "column": 7}, {"title": "should add new comments", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-ca40a2318d7dffd2b2bf", "file": "review.spec.ts", "line": 75, "column": 7}, {"title": "should clear line selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-171d60f2d1c5ae9800fc", "file": "review.spec.ts", "line": 88, "column": 7}, {"title": "should display version history", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-15c164c6c27b971c64f4", "file": "review.spec.ts", "line": 98, "column": 7}, {"title": "should switch between versions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-022a879415ce946b1003", "file": "review.spec.ts", "line": 109, "column": 7}, {"title": "should display all comments tab", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-5331a87863f583d5eef7", "file": "review.spec.ts", "line": 121, "column": 7}, {"title": "should display review summary", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-0538e2bd1f44fdf46397", "file": "review.spec.ts", "line": 134, "column": 7}, {"title": "should approve version with keyboard shortcut", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-ba55c2cdfb9a6e01f8dd", "file": "review.spec.ts", "line": 143, "column": 7}, {"title": "should request changes with keyboard shortcut", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-fde31f2f8c107c4d9d6f", "file": "review.spec.ts", "line": 152, "column": 7}, {"title": "should approve version with button", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-f9fcf5414359bba908b4", "file": "review.spec.ts", "line": 161, "column": 7}, {"title": "should request changes with button", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-a5e922479200c13f7323", "file": "review.spec.ts", "line": 169, "column": 7}, {"title": "should display keyboard shortcut indicators on buttons", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-5756fcc19e8e71b55329", "file": "review.spec.ts", "line": 177, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-b3e38423bed398e6c357", "file": "review.spec.ts", "line": 183, "column": 7}, {"title": "should display help button with review context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "12f883ce49bb8436c3d7-8b83b1028661dfde44cb", "file": "review.spec.ts", "line": 191, "column": 7}]}]}, {"title": "signatures.spec.ts", "file": "signatures.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Signature Tracker", "file": "signatures.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should display signature tracker interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-b76df870a0fad9236976", "file": "signatures.spec.ts", "line": 17, "column": 7}, {"title": "should display signature requests list", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-7ba66b3addf28bdec72c", "file": "signatures.spec.ts", "line": 26, "column": 7}, {"title": "should display progress bars for signature requests", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-6e86f32c443d68ce829a", "file": "signatures.spec.ts", "line": 38, "column": 7}, {"title": "should display party avatars and status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-f8fa20e19a5ec4930f05", "file": "signatures.spec.ts", "line": 46, "column": 7}, {"title": "should select signature request and show details", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-22e1b527d54fb2b68968", "file": "signatures.spec.ts", "line": 55, "column": 7}, {"title": "should display signing parties in details panel", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-8fadf96d2f205af04fa4", "file": "signatures.spec.ts", "line": 71, "column": 7}, {"title": "should show signed party information", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-434761b4d625646d4bfe", "file": "signatures.spec.ts", "line": 87, "column": 7}, {"title": "should display send reminder button for pending parties", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-e105fbb19a92f1f2170d", "file": "signatures.spec.ts", "line": 96, "column": 7}, {"title": "should send reminder when button clicked", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-a8ee6ae9b4af5ab54872", "file": "signatures.spec.ts", "line": 104, "column": 7}, {"title": "should display action buttons", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-eb2261f288a6d14b6fe3", "file": "signatures.spec.ts", "line": 116, "column": 7}, {"title": "should resend document when button clicked", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-59f3d435f9ebf3a83a0d", "file": "signatures.spec.ts", "line": 126, "column": 7}, {"title": "should show placeholder when no request selected", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-3ec4fa760dce0e2f5744", "file": "signatures.spec.ts", "line": 138, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-d4b3a7eb8b49514a0a56", "file": "signatures.spec.ts", "line": 143, "column": 7}, {"title": "should display help button with signature context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-21c47864a64447f1418e", "file": "signatures.spec.ts", "line": 151, "column": 7}, {"title": "should maintain selection across interactions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-e179893fe625468c3c6c", "file": "signatures.spec.ts", "line": 162, "column": 7}, {"title": "should display signature tracker interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-03efb350a749e42ce93a", "file": "signatures.spec.ts", "line": 17, "column": 7}, {"title": "should display signature requests list", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-84bcd62d8b8414457500", "file": "signatures.spec.ts", "line": 26, "column": 7}, {"title": "should display progress bars for signature requests", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-f00f6e098ea086998602", "file": "signatures.spec.ts", "line": 38, "column": 7}, {"title": "should display party avatars and status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-3f88e0832c88afa0634d", "file": "signatures.spec.ts", "line": 46, "column": 7}, {"title": "should select signature request and show details", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-f681cc2999cf05a6cce6", "file": "signatures.spec.ts", "line": 55, "column": 7}, {"title": "should display signing parties in details panel", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-ca5cd7abaf4ed3c6bf2c", "file": "signatures.spec.ts", "line": 71, "column": 7}, {"title": "should show signed party information", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-36c342a8f0c73b279ae4", "file": "signatures.spec.ts", "line": 87, "column": 7}, {"title": "should display send reminder button for pending parties", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-b28f256c254f7b9eba95", "file": "signatures.spec.ts", "line": 96, "column": 7}, {"title": "should send reminder when button clicked", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-358b7ee47f48b91bc8fd", "file": "signatures.spec.ts", "line": 104, "column": 7}, {"title": "should display action buttons", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-ad3aec7cf886e0da7e7d", "file": "signatures.spec.ts", "line": 116, "column": 7}, {"title": "should resend document when button clicked", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-7050743f1c4f9138590e", "file": "signatures.spec.ts", "line": 126, "column": 7}, {"title": "should show placeholder when no request selected", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-38f63c77c9a1ef56cb1c", "file": "signatures.spec.ts", "line": 138, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-d8619581a8d5e98db143", "file": "signatures.spec.ts", "line": 143, "column": 7}, {"title": "should display help button with signature context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-cf46b76f5854a7b9e350", "file": "signatures.spec.ts", "line": 151, "column": 7}, {"title": "should maintain selection across interactions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-d3e0f5d55275592c3f9f", "file": "signatures.spec.ts", "line": 162, "column": 7}, {"title": "should display signature tracker interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-0592ccb753c182547fc4", "file": "signatures.spec.ts", "line": 17, "column": 7}, {"title": "should display signature requests list", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-53e8b93754eb711bc8e4", "file": "signatures.spec.ts", "line": 26, "column": 7}, {"title": "should display progress bars for signature requests", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-64cdb99e5a5e7cd35194", "file": "signatures.spec.ts", "line": 38, "column": 7}, {"title": "should display party avatars and status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-6c56a59b10589a7c80d3", "file": "signatures.spec.ts", "line": 46, "column": 7}, {"title": "should select signature request and show details", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-51e15708fae6e6d718e8", "file": "signatures.spec.ts", "line": 55, "column": 7}, {"title": "should display signing parties in details panel", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-6c504fb54762ed4ef8de", "file": "signatures.spec.ts", "line": 71, "column": 7}, {"title": "should show signed party information", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-e94104ea91bfab16708a", "file": "signatures.spec.ts", "line": 87, "column": 7}, {"title": "should display send reminder button for pending parties", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-2303bbb688c10db1ea19", "file": "signatures.spec.ts", "line": 96, "column": 7}, {"title": "should send reminder when button clicked", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-a5359001eba3a5b6cb44", "file": "signatures.spec.ts", "line": 104, "column": 7}, {"title": "should display action buttons", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-a476e5a5f3a11787b479", "file": "signatures.spec.ts", "line": 116, "column": 7}, {"title": "should resend document when button clicked", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-6660b355e605b522da16", "file": "signatures.spec.ts", "line": 126, "column": 7}, {"title": "should show placeholder when no request selected", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-4234415c5c8fe18920ec", "file": "signatures.spec.ts", "line": 138, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-cdc21df638c4ea3d346f", "file": "signatures.spec.ts", "line": 143, "column": 7}, {"title": "should display help button with signature context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-80549ec38bf20d97072b", "file": "signatures.spec.ts", "line": 151, "column": 7}, {"title": "should maintain selection across interactions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-107feb3f4ece76936834", "file": "signatures.spec.ts", "line": 162, "column": 7}, {"title": "should display signature tracker interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-c90df02a6c16c1e39cbd", "file": "signatures.spec.ts", "line": 17, "column": 7}, {"title": "should display signature requests list", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-19c73280c967da67fac0", "file": "signatures.spec.ts", "line": 26, "column": 7}, {"title": "should display progress bars for signature requests", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-fb2554380b2b42280952", "file": "signatures.spec.ts", "line": 38, "column": 7}, {"title": "should display party avatars and status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-3359e28315f14005ec04", "file": "signatures.spec.ts", "line": 46, "column": 7}, {"title": "should select signature request and show details", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-f042d45c37d5e66fcd15", "file": "signatures.spec.ts", "line": 55, "column": 7}, {"title": "should display signing parties in details panel", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-185d9f4eae84b3efe5cc", "file": "signatures.spec.ts", "line": 71, "column": 7}, {"title": "should show signed party information", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-39a8d9b286ee3f9c3658", "file": "signatures.spec.ts", "line": 87, "column": 7}, {"title": "should display send reminder button for pending parties", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-91a811f54ec376db5935", "file": "signatures.spec.ts", "line": 96, "column": 7}, {"title": "should send reminder when button clicked", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-9abefdda30e119ecff99", "file": "signatures.spec.ts", "line": 104, "column": 7}, {"title": "should display action buttons", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-80f995541040b46e8909", "file": "signatures.spec.ts", "line": 116, "column": 7}, {"title": "should resend document when button clicked", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-a88f60865e744875e7e7", "file": "signatures.spec.ts", "line": 126, "column": 7}, {"title": "should show placeholder when no request selected", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-383e74b796b1bc397d8b", "file": "signatures.spec.ts", "line": 138, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-0ea5a08ec69f601a26ee", "file": "signatures.spec.ts", "line": 143, "column": 7}, {"title": "should display help button with signature context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-38963e6e466b9b7e768b", "file": "signatures.spec.ts", "line": 151, "column": 7}, {"title": "should maintain selection across interactions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-9018b2fa3d4e5f832200", "file": "signatures.spec.ts", "line": 162, "column": 7}, {"title": "should display signature tracker interface", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-3b7ae30c51ab0625ac65", "file": "signatures.spec.ts", "line": 17, "column": 7}, {"title": "should display signature requests list", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-2c76f180e9f83158c50a", "file": "signatures.spec.ts", "line": 26, "column": 7}, {"title": "should display progress bars for signature requests", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-e4085d27171c790706ed", "file": "signatures.spec.ts", "line": 38, "column": 7}, {"title": "should display party avatars and status", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-012b05873f4879873ca3", "file": "signatures.spec.ts", "line": 46, "column": 7}, {"title": "should select signature request and show details", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-60b5c52ea19d6c053e55", "file": "signatures.spec.ts", "line": 55, "column": 7}, {"title": "should display signing parties in details panel", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-028bc6398d819a299c9f", "file": "signatures.spec.ts", "line": 71, "column": 7}, {"title": "should show signed party information", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-7071b34a3c4d57734cc1", "file": "signatures.spec.ts", "line": 87, "column": 7}, {"title": "should display send reminder button for pending parties", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-dd7bda215d59b10c1048", "file": "signatures.spec.ts", "line": 96, "column": 7}, {"title": "should send reminder when button clicked", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-e9c4db93b742ed38856c", "file": "signatures.spec.ts", "line": 104, "column": 7}, {"title": "should display action buttons", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-49e98866902f85f80529", "file": "signatures.spec.ts", "line": 116, "column": 7}, {"title": "should resend document when button clicked", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-d1c4460e12daf8e2228e", "file": "signatures.spec.ts", "line": 126, "column": 7}, {"title": "should show placeholder when no request selected", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-1fd93a1e36581e394971", "file": "signatures.spec.ts", "line": 138, "column": 7}, {"title": "should be responsive on mobile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-1c7a1f5be4d23426b70f", "file": "signatures.spec.ts", "line": 143, "column": 7}, {"title": "should display help button with signature context", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-3119e9c1cab6bde5cfc0", "file": "signatures.spec.ts", "line": 151, "column": 7}, {"title": "should maintain selection across interactions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "5df4bcca6e772909ce79-668a422dec62886e4137", "file": "signatures.spec.ts", "line": 162, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-08-02T19:05:09.991Z", "duration": 171.15600000000006, "expected": 0, "skipped": 450, "unexpected": 0, "flaky": 0}}