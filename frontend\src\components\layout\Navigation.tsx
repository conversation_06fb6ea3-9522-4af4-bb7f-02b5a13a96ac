"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ThemeToggle } from "@/components/ui/theme-toggle"
import { useToast } from "@/hooks/use-toast"
import { useAuthStore } from "@/stores/auth"
import Link from "next/link"
import { useRouter } from "next/navigation"

interface NavigationProps {
  className?: string
}

export function Navigation({ className }: NavigationProps) {
  const router = useRouter()
  const { toast } = useToast()
  const { user, isAuthenticated, logout } = useAuthStore()

  const handleLogout = async () => {
    logout()
    toast({
      title: "Logged out",
      description: "You have been logged out successfully.",
    })
    router.push("/login")
  }

  if (!isAuthenticated || !user) {
    return null
  }

  const navigationItems = [
    { href: "/dashboard", label: "Dashboard" },
    { href: "/documents", label: "Documents" },
    { href: "/contracts", label: "Contracts" },
    { href: "/review", label: "Review" },
    { href: "/signatures", label: "Signatures" },
  ]

  // Add admin-only items
  if (user.role === 'admin') {
    navigationItems.push({ href: "/admin", label: "Admin" })
  }

  return (
    <nav className={`bg-background border-b border-border shadow-sm ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-6">
          {/* Logo and Brand */}
          <div className="flex items-center">
            <Link href="/dashboard" className="flex items-center">
              <h1 className="text-2xl font-bold text-foreground">RealtorAgentAI</h1>
            </Link>
          </div>

          {/* Navigation Links */}
          <div className="hidden md:flex items-center space-x-8">
            {navigationItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className="text-muted-foreground hover:text-foreground px-3 py-2 rounded-md text-sm font-medium transition-colors"
              >
                {item.label}
              </Link>
            ))}
          </div>

          {/* User Menu */}
          <div className="flex items-center space-x-4">
            <span className="text-sm text-muted-foreground">
              Welcome, {user.name}
            </span>
          <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
            {user.role}
          </span>
          <ThemeToggle />
          <Button onClick={handleLogout} variant="outline" size="sm">
            Logout
          </Button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      <div className="md:hidden">
        <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-border">
          {navigationItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className="text-muted-foreground hover:text-foreground block px-3 py-2 rounded-md text-base font-medium transition-colors"
            >
              {item.label}
            </Link>
          ))}
        </div>
      </div>
    </nav>
  )
}
