"""Initial database schema

Revision ID: 73c8d4e17048
Revises:
Create Date: 2025-08-03 09:44:43.411877

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = '73c8d4e17048'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('templates',
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('version', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('docx_key', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('schema', sa.JSON(), nullable=True),
    sa.Column('ruleset', sa.JSON(), nullable=True),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('category', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('preview_image_key', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('usage_count', sa.Integer(), nullable=False),
    sa.Column('last_used', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_templates_created_at'), 'templates', ['created_at'], unique=False)
    op.create_table('users',
    sa.Column('email', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('role', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('disabled', sa.Boolean(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('password_hash', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('last_login', sa.DateTime(), nullable=True),
    sa.Column('login_count', sa.Integer(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_created_at'), 'users', ['created_at'], unique=False)
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_table('deals',
    sa.Column('title', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=False),
    sa.Column('status', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('owner_id', sa.Integer(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('closed_at', sa.DateTime(), nullable=True),
    sa.Column('property_address', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True),
    sa.Column('deal_value', sa.Float(), nullable=True),
    sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_deals_created_at'), 'deals', ['created_at'], unique=False)
    op.create_index(op.f('ix_deals_owner_id'), 'deals', ['owner_id'], unique=False)
    op.create_table('audit_logs',
    sa.Column('actor', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('action', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('ts', sa.DateTime(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('meta', sa.JSON(), nullable=True),
    sa.Column('deal_id', sa.Integer(), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('resource_type', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('resource_id', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('ip_address', sqlmodel.sql.sqltypes.AutoString(length=45), nullable=True),
    sa.Column('user_agent', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('session_id', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('success', sa.Boolean(), nullable=False),
    sa.Column('error_message', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('before_state', sa.JSON(), nullable=True),
    sa.Column('after_state', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['deal_id'], ['deals.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_audit_logs_deal_id'), 'audit_logs', ['deal_id'], unique=False)
    op.create_index(op.f('ix_audit_logs_user_id'), 'audit_logs', ['user_id'], unique=False)
    op.create_table('contracts',
    sa.Column('status', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('deal_id', sa.Integer(), nullable=False),
    sa.Column('template_id', sa.Integer(), nullable=False),
    sa.Column('variables', sa.JSON(), nullable=True),
    sa.Column('title', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('generated_docx_key', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('generated_pdf_key', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('sent_for_signature_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['deal_id'], ['deals.id'], ),
    sa.ForeignKeyConstraint(['template_id'], ['templates.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_contracts_created_at'), 'contracts', ['created_at'], unique=False)
    op.create_index(op.f('ix_contracts_deal_id'), 'contracts', ['deal_id'], unique=False)
    op.create_index(op.f('ix_contracts_template_id'), 'contracts', ['template_id'], unique=False)
    op.create_table('uploads',
    sa.Column('filename', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('s3_key', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=False),
    sa.Column('content_type', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('pages', sa.Integer(), nullable=True),
    sa.Column('ocr', sa.Boolean(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('deal_id', sa.Integer(), nullable=False),
    sa.Column('file_size', sa.Integer(), nullable=True),
    sa.Column('checksum', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('processing_status', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('processing_error', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('extracted_text', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('extraction_confidence', sa.Float(), nullable=True),
    sa.ForeignKeyConstraint(['deal_id'], ['deals.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_uploads_created_at'), 'uploads', ['created_at'], unique=False)
    op.create_index(op.f('ix_uploads_deal_id'), 'uploads', ['deal_id'], unique=False)
    op.create_table('signers',
    sa.Column('party_role', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('email', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('phone', sqlmodel.sql.sqltypes.AutoString(length=20), nullable=True),
    sa.Column('order', sa.Integer(), nullable=False),
    sa.Column('status', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('contract_id', sa.Integer(), nullable=False),
    sa.Column('provider_signer_id', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('provider_envelope_id', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('sent_at', sa.DateTime(), nullable=True),
    sa.Column('viewed_at', sa.DateTime(), nullable=True),
    sa.Column('signed_at', sa.DateTime(), nullable=True),
    sa.Column('declined_at', sa.DateTime(), nullable=True),
    sa.Column('decline_reason', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('ip_address', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('user_agent', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.ForeignKeyConstraint(['contract_id'], ['contracts.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_signers_contract_id'), 'signers', ['contract_id'], unique=False)
    op.create_index(op.f('ix_signers_created_at'), 'signers', ['created_at'], unique=False)
    op.create_table('validations',
    sa.Column('rule_id', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    sa.Column('severity', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('ok', sa.Boolean(), nullable=False),
    sa.Column('detail', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('contract_id', sa.Integer(), nullable=False),
    sa.Column('rule_name', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('rule_category', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('execution_time_ms', sa.Integer(), nullable=True),
    sa.Column('field_path', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('expected_value', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('actual_value', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('suggestion', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('auto_fixable', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['contract_id'], ['contracts.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_validations_contract_id'), 'validations', ['contract_id'], unique=False)
    op.create_index(op.f('ix_validations_created_at'), 'validations', ['created_at'], unique=False)
    op.create_table('versions',
    sa.Column('number', sa.Integer(), nullable=False),
    sa.Column('diff', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('contract_id', sa.Integer(), nullable=False),
    sa.Column('created_by', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('change_summary', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('content_hash', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('file_key', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('is_current', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['contract_id'], ['contracts.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_versions_contract_id'), 'versions', ['contract_id'], unique=False)
    op.create_index(op.f('ix_versions_created_at'), 'versions', ['created_at'], unique=False)
    op.create_table('sign_events',
    sa.Column('type', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('ip', sqlmodel.sql.sqltypes.AutoString(length=45), nullable=True),
    sa.Column('ua', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('ts', sa.DateTime(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('signer_id', sa.Integer(), nullable=False),
    sa.Column('meta', sa.JSON(), nullable=True),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('error_message', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('provider_event_id', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('provider_status', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('location', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.ForeignKeyConstraint(['signer_id'], ['signers.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_sign_events_signer_id'), 'sign_events', ['signer_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_sign_events_signer_id'), table_name='sign_events')
    op.drop_table('sign_events')
    op.drop_index(op.f('ix_versions_created_at'), table_name='versions')
    op.drop_index(op.f('ix_versions_contract_id'), table_name='versions')
    op.drop_table('versions')
    op.drop_index(op.f('ix_validations_created_at'), table_name='validations')
    op.drop_index(op.f('ix_validations_contract_id'), table_name='validations')
    op.drop_table('validations')
    op.drop_index(op.f('ix_signers_created_at'), table_name='signers')
    op.drop_index(op.f('ix_signers_contract_id'), table_name='signers')
    op.drop_table('signers')
    op.drop_index(op.f('ix_uploads_deal_id'), table_name='uploads')
    op.drop_index(op.f('ix_uploads_created_at'), table_name='uploads')
    op.drop_table('uploads')
    op.drop_index(op.f('ix_contracts_template_id'), table_name='contracts')
    op.drop_index(op.f('ix_contracts_deal_id'), table_name='contracts')
    op.drop_index(op.f('ix_contracts_created_at'), table_name='contracts')
    op.drop_table('contracts')
    op.drop_index(op.f('ix_audit_logs_user_id'), table_name='audit_logs')
    op.drop_index(op.f('ix_audit_logs_deal_id'), table_name='audit_logs')
    op.drop_table('audit_logs')
    op.drop_index(op.f('ix_deals_owner_id'), table_name='deals')
    op.drop_index(op.f('ix_deals_created_at'), table_name='deals')
    op.drop_table('deals')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_index(op.f('ix_users_created_at'), table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_templates_created_at'), table_name='templates')
    op.drop_table('templates')
    # ### end Alembic commands ###
