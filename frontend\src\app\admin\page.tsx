"use client"

import { ProtectedRoute } from "@/components/auth/ProtectedRoute"
import { Navigation } from "@/components/layout/Navigation"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/hooks/use-toast"
import { useEffect, useState } from "react"

interface User {
  id: string
  name: string
  email: string
  role: 'admin' | 'agent' | 'tc' | 'signer'
  status: 'active' | 'inactive'
  lastLogin: string
  createdAt: string
}

interface TemplateVersion {
  id: string
  version: string
  createdAt: string
  author: string
}

interface Template {
  id: string
  name: string
  version: string
  category: string
  status: 'active' | 'draft' | 'archived'
  lastModified: string
  author: string
  usageCount: number
  versions: TemplateVersion[]
}

interface AuditLog {
  id: string
  timestamp: string
  user: string
  action: string
  resource: string
  details: string
  ipAddress: string
}

const MOCK_USERS: User[] = [
  {
    id: '1',
    name: 'John Smith',
    email: '<EMAIL>',
    role: 'agent',
    status: 'active',
    lastLogin: '2024-01-16T10:30:00Z',
    createdAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    name: 'Jane Doe',
    email: '<EMAIL>',
    role: 'agent',
    status: 'active',
    lastLogin: '2024-01-16T14:15:00Z',
    createdAt: '2024-01-02T00:00:00Z'
  },
  {
    id: '3',
    name: 'Mike Johnson',
    email: '<EMAIL>',
    role: 'tc',
    status: 'active',
    lastLogin: '2024-01-15T16:45:00Z',
    createdAt: '2024-01-03T00:00:00Z'
  }
]

const MOCK_TEMPLATES: Template[] = [
  {
    id: '1',
    name: 'Residential Purchase Agreement',
    version: '2.1',
    category: 'Purchase',
    status: 'active',
    lastModified: '2024-01-15T12:00:00Z',
    author: 'Admin User',
    usageCount: 45,
    versions: [
      { id: 'v1', version: '2.0', createdAt: '2023-12-01T00:00:00Z', author: 'Admin User' },
      { id: 'v2', version: '2.1', createdAt: '2024-01-15T12:00:00Z', author: 'Admin User' }
    ]
  },
  {
    id: '2',
    name: 'Listing Agreement',
    version: '1.8',
    category: 'Listing',
    status: 'active',
    lastModified: '2024-01-10T09:30:00Z',
    author: 'Admin User',
    usageCount: 32,
    versions: [
      { id: 'v1', version: '1.7', createdAt: '2023-11-15T00:00:00Z', author: 'Admin User' },
      { id: 'v2', version: '1.8', createdAt: '2024-01-10T09:30:00Z', author: 'Admin User' }
    ]
  }
]

const MOCK_AUDIT_LOGS: AuditLog[] = [
  {
    id: '1',
    timestamp: '2024-01-16T15:30:00Z',
    user: '<EMAIL>',
    action: 'CONTRACT_GENERATED',
    resource: 'Purchase Agreement - 123 Main St',
    details: 'Generated contract from template',
    ipAddress: '*************'
  },
  {
    id: '2',
    timestamp: '2024-01-16T14:15:00Z',
    user: '<EMAIL>',
    action: 'DOCUMENT_UPLOADED',
    resource: 'Property Disclosure.pdf',
    details: 'Uploaded document for processing',
    ipAddress: '*************'
  }
]

const ROLE_PERMISSIONS: Record<string, string[]> = {
  admin: ['manage_users', 'manage_templates', 'view_audit', 'configure_system'],
  agent: ['create_contracts', 'upload_documents'],
  tc: ['manage_deals', 'send_signatures'],
  signer: ['sign_documents']
}

export default function AdminPage() {
  const [users, setUsers] = useState<User[]>(MOCK_USERS)
  const [templates, setTemplates] = useState<Template[]>(MOCK_TEMPLATES)
  const [auditLogs] = useState<AuditLog[]>(MOCK_AUDIT_LOGS)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [selectedUserRole, setSelectedUserRole] = useState<string>()
  const [searchQuery, setSearchQuery] = useState("")
  const [modelRoutes, setModelRoutes] = useState([{ id: 'primary', model: 'gpt-4', weight: 100 }])
  const [temperature, setTemperature] = useState(0.7)
  const [tokenLimit, setTokenLimit] = useState(4000)
  const [metrics, setMetrics] = useState({ cpu: 0, memory: 0 })
  const [openTemplate, setOpenTemplate] = useState<string | null>(null)
  const { toast } = useToast()

  const refreshMetrics = () => {
    setMetrics({
      cpu: Math.floor(Math.random() * 100),
      memory: Math.floor(Math.random() * 100)
    })
  }

  useEffect(() => {
    refreshMetrics()
  }, [])

  const handleUserStatusToggle = (userId: string) => {
    setUsers(prev => prev.map(user =>
      user.id === userId
        ? { ...user, status: user.status === 'active' ? 'inactive' : 'active' }
        : user
    ))

    toast({
      title: "User Status Updated",
      description: "User status has been changed successfully.",
    })
  }

  const handleTemplateStatusChange = (templateId: string, newStatus: 'active' | 'draft' | 'archived') => {
    setTemplates(prev => prev.map(template =>
      template.id === templateId
        ? { ...template, status: newStatus }
        : template
    ))

    toast({
      title: "Template Status Updated",
      description: "Template status has been changed successfully.",
    })
  }

  const handleUserSelect = (user: User) => {
    setSelectedUser(user)
    setSelectedUserRole(user.role)
  }

  const handleUserRoleSave = () => {
    if (!selectedUser || !selectedUserRole) return
    const validRole = selectedUserRole as 'admin' | 'agent' | 'tc' | 'signer'
    setUsers(prev => prev.map(u => (u.id === selectedUser.id ? { ...u, role: validRole } : u)))
    setSelectedUser({ ...selectedUser, role: validRole })
    toast({ title: "User Updated", description: "User role saved." })
  }

  const addRoute = () => {
    setModelRoutes(prev => [...prev, { id: Date.now().toString(), model: '', weight: 0 }])
  }

  const updateRoute = (id: string, field: 'model' | 'weight', value: string) => {
    setModelRoutes(prev =>
      prev.map(r => (r.id === id ? { ...r, [field]: field === 'weight' ? Number(value) : value } : r))
    )
  }

  const removeRoute = (id: string) => {
    setModelRoutes(prev => prev.filter(r => r.id !== id))
  }

  const handleTemplateNewVersion = (templateId: string) => {
    setTemplates(prev =>
      prev.map(t =>
        t.id === templateId
          ? {
              ...t,
              version: (parseFloat(t.version) + 0.1).toFixed(1),
              versions: [
                ...t.versions,
                {
                  id: `version-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                  version: (parseFloat(t.version) + 0.1).toFixed(1),
                  createdAt: new Date().toISOString(),
                  author: 'Admin User'
                }
              ]
            }
          : t
      )
    )
    toast({ title: "New Version Created", description: "Template version incremented." })
  }

  const exportAuditLogs = () => {
    const rows = filteredAuditLogs.map(l =>
      [l.timestamp, l.user, l.action, l.resource, l.details, l.ipAddress].join(',')
    )
    const csv = ['timestamp,user,action,resource,details,ip', ...rows].join('\n')
    const blob = new Blob([csv], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'audit_logs.csv'
    a.click()
    URL.revokeObjectURL(url)
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'default'
      case 'agent': return 'secondary'
      case 'tc': return 'outline'
      default: return 'secondary'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'default'
      case 'inactive': return 'destructive'
      case 'draft': return 'secondary'
      case 'archived': return 'outline'
      default: return 'secondary'
    }
  }

  const filteredAuditLogs = auditLogs.filter(log =>
    log.user.toLowerCase().includes(searchQuery.toLowerCase()) ||
    log.action.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <ProtectedRoute requiredRole="admin">
      <div className="min-h-screen bg-background">
        <Navigation />

        {/* Page Header */}
        <header className="bg-card shadow-sm border-b border-border">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="py-6">
              <h1 className="text-3xl font-bold text-foreground">Admin Panel</h1>
              <p className="text-sm text-muted-foreground mt-1">
                Manage users, templates, system configuration, and audit trails
              </p>
            </div>
          </div>
        </header>

        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <Tabs defaultValue="users" className="space-y-6">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="users">User Management</TabsTrigger>
                <TabsTrigger value="templates">Templates</TabsTrigger>
                <TabsTrigger value="system">System Config</TabsTrigger>
                <TabsTrigger value="audit">Audit Logs</TabsTrigger>
              </TabsList>

              {/* User Management */}
              <TabsContent value="users" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <div className="lg:col-span-2">
                    <Card>
                      <CardHeader>
                        <CardTitle>Users ({users.length})</CardTitle>
                        <CardDescription>
                          Manage user accounts and permissions
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          {users.map((user) => (
                            <div
                              key={user.id}
                              className={`border rounded-lg p-4 cursor-pointer transition-colors hover:bg-gray-50 ${
                                selectedUser?.id === user.id ? 'ring-2 ring-blue-500' : ''
                              }`}
                              onClick={() => handleUserSelect(user)}
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                  <Avatar className="w-10 h-10">
                                    <AvatarFallback>
                                      {user.name.split(' ').map(n => n[0]).join('')}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div>
                                    <h3 className="font-medium">{user.name}</h3>
                                    <p className="text-sm text-gray-600">{user.email}</p>
                                    <p className="text-xs text-gray-500">
                                      Last login: {new Date(user.lastLogin).toLocaleString()}
                                    </p>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Badge variant={getRoleColor(user.role)}>
                                    {user.role}
                                  </Badge>
                                  <Badge variant={getStatusColor(user.status)}>
                                    {user.status}
                                  </Badge>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation()
                                      handleUserStatusToggle(user.id)
                                    }}
                                  >
                                    {user.status === 'active' ? 'Deactivate' : 'Activate'}
                                  </Button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <div className="lg:col-span-1">
                    {selectedUser ? (
                      <Card>
                        <CardHeader>
                          <CardTitle>User Details</CardTitle>
                          <CardDescription>
                            {selectedUser.name}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            <div>
                              <Label htmlFor="name">Name</Label>
                              <Input id="name" value={selectedUser.name} readOnly />
                            </div>
                            <div>
                              <Label htmlFor="email">Email</Label>
                              <Input id="email" value={selectedUser.email} readOnly />
                            </div>
                            <div>
                              <Label htmlFor="role">Role</Label>
                              <Select value={selectedUserRole} onValueChange={setSelectedUserRole}>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="admin">Admin</SelectItem>
                                  <SelectItem value="agent">Agent</SelectItem>
                                  <SelectItem value="tc">Transaction Coordinator</SelectItem>
                                  <SelectItem value="signer">Signer</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div>
                              <Label>Permissions</Label>
                              <div className="flex flex-wrap gap-2 mt-1">
                                {ROLE_PERMISSIONS[selectedUserRole ?? selectedUser.role].map(p => (
                                  <Badge key={p} variant="outline">{p}</Badge>
                                ))}
                              </div>
                            </div>
                            <div>
                              <Label>Status</Label>
                              <div className="mt-1">
                                <Badge variant={getStatusColor(selectedUser.status)}>
                                  {selectedUser.status}
                                </Badge>
                              </div>
                            </div>
                            <div>
                              <Label>Created</Label>
                              <p className="text-sm text-gray-600 mt-1">
                                {new Date(selectedUser.createdAt).toLocaleDateString()}
                              </p>
                            </div>
                            <Button className="w-full" onClick={handleUserRoleSave}>
                              Save Changes
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ) : (
                      <Card>
                        <CardContent className="flex items-center justify-center h-64">
                          <p className="text-gray-500">Select a user to view details</p>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                </div>
              </TabsContent>

              {/* Template Management */}
              <TabsContent value="templates" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Contract Templates ({templates.length})</CardTitle>
                    <CardDescription>
                      Manage contract templates and versions
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {templates.map((template) => (
                        <div key={template.id} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h3 className="font-medium cursor-pointer" onClick={() => setOpenTemplate(openTemplate === template.id ? null : template.id)}>{template.name}</h3>
                              <p className="text-sm text-gray-600">Version {template.version}</p>
                              <p className="text-xs text-gray-500">
                                Modified: {new Date(template.lastModified).toLocaleString()} by {template.author}
                              </p>
                              <p className="text-xs text-gray-500">
                                Used {template.usageCount} times
                              </p>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Badge variant="outline">{template.category}</Badge>
                              <Badge variant={getStatusColor(template.status)}>
                                {template.status}
                              </Badge>
                              <Select
                                value={template.status}
                                onValueChange={(value: 'active' | 'draft' | 'archived') =>
                                  handleTemplateStatusChange(template.id, value)
                                }
                              >
                                <SelectTrigger className="w-32">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="active">Active</SelectItem>
                                  <SelectItem value="draft">Draft</SelectItem>
                                  <SelectItem value="archived">Archived</SelectItem>
                                </SelectContent>
                              </Select>
                              <Button variant="outline" size="sm" onClick={() => handleTemplateNewVersion(template.id)}>
                                New Version
                              </Button>
                            </div>
                          </div>
                          {openTemplate === template.id && (
                            <div className="mt-4 space-y-2">
                              {template.versions.map(v => (
                                <div key={v.id} className="text-xs text-gray-600">
                                  v{v.version} - {new Date(v.createdAt).toLocaleString()} by {v.author}
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* System Configuration */}
              <TabsContent value="system" className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <Card className="md:col-span-1">
                    <CardHeader>
                      <CardTitle>AI Model Configuration</CardTitle>
                      <CardDescription>
                        Configure AI model routing and settings
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {modelRoutes.map(route => (
                          <div key={route.id} className="flex items-center space-x-2">
                            <Input
                              placeholder="model id"
                              value={route.model}
                              onChange={e => updateRoute(route.id, 'model', e.target.value)}
                              className="flex-1"
                            />
                            <Input
                              type="number"
                              value={route.weight}
                              onChange={e => updateRoute(route.id, 'weight', e.target.value)}
                              className="w-24"
                            />
                            <Button variant="outline" size="sm" onClick={() => removeRoute(route.id)}>
                              Remove
                            </Button>
                          </div>
                        ))}
                        <Button variant="outline" size="sm" onClick={addRoute}>
                          Add Route
                        </Button>
                        <div>
                          <Label htmlFor="temperature">Temperature</Label>
                          <Input
                            id="temperature"
                            type="number"
                            step="0.1"
                            value={temperature}
                            onChange={e => setTemperature(parseFloat(e.target.value))}
                          />
                        </div>
                        <div>
                          <Label htmlFor="token-limit">Token Limit</Label>
                          <Input
                            id="token-limit"
                            type="number"
                            value={tokenLimit}
                            onChange={e => setTokenLimit(parseInt(e.target.value))}
                          />
                        </div>
                        <Button onClick={() => toast({ title: 'Configuration Saved' })}>Save Configuration</Button>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="md:col-span-1">
                    <CardHeader>
                      <CardTitle>System Settings</CardTitle>
                      <CardDescription>
                        General system configuration
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="max-file-size">Max File Size (MB)</Label>
                          <Input id="max-file-size" type="number" defaultValue="10" />
                        </div>
                        <div>
                          <Label htmlFor="session-timeout">Session Timeout (hours)</Label>
                          <Input id="session-timeout" type="number" defaultValue="8" />
                        </div>
                        <div>
                          <Label htmlFor="backup-retention">Backup Retention (days)</Label>
                          <Input id="backup-retention" type="number" defaultValue="30" />
                        </div>
                        <Button>Save Settings</Button>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="md:col-span-1">
                    <CardHeader>
                      <CardTitle>System Metrics</CardTitle>
                      <CardDescription>Current performance indicators</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <p className="text-sm">CPU Usage: {metrics.cpu}%</p>
                        <p className="text-sm">Memory Usage: {metrics.memory}%</p>
                        <Button variant="outline" size="sm" onClick={refreshMetrics}>Refresh</Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Audit Logs */}
              <TabsContent value="audit" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Audit Trail ({filteredAuditLogs.length})</CardTitle>
                    <CardDescription>
                      System activity and security audit logs
                    </CardDescription>
                    <div className="flex items-center space-x-2 mt-4">
                      <Input
                        placeholder="Search by user or action"
                        value={searchQuery}
                        onChange={e => setSearchQuery(e.target.value)}
                      />
                      <Button variant="outline" size="sm" onClick={exportAuditLogs}>Export CSV</Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {filteredAuditLogs.map((log) => (
                        <div key={log.id} className="border rounded-lg p-4">
                          <div className="flex items-start justify-between">
                            <div>
                              <div className="flex items-center space-x-2">
                                <Badge variant="outline">{log.action}</Badge>
                                <span className="text-sm font-medium">{log.user}</span>
                              </div>
                              <p className="text-sm text-gray-600 mt-1">{log.resource}</p>
                              <p className="text-xs text-gray-500 mt-1">{log.details}</p>
                              <p className="text-xs text-gray-400 mt-1">
                                IP: {log.ipAddress}
                              </p>
                            </div>
                            <div className="text-xs text-gray-500">
                              {new Date(log.timestamp).toLocaleString()}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  )
}
