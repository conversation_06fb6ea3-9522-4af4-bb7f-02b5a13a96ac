"""
API module for the Multi-Agent Real-Estate Contract Platform.

This module contains all API endpoints organized by functionality:
- auth: Authentication and user management
- files: File upload and storage
- contracts: Contract management and generation
- signatures: E-signature workflows
- admin: Administrative operations
- ai_agents: AI agent operations

Each submodule contains FastAPI routers that are included in the main application.
"""

# API routers will be imported here as they are implemented
# from .auth import router as auth_router
# from .files import router as files_router
# from .contracts import router as contracts_router
# from .signatures import router as signatures_router
# from .admin import router as admin_router
# from .ai_agents import router as ai_agents_router

__all__ = [
    # Routers will be exported here as they are implemented
    # "auth_router",
    # "files_router", 
    # "contracts_router",
    # "signatures_router",
    # "admin_router",
    # "ai_agents_router",
]
