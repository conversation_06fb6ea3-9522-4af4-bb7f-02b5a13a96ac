[tool:pytest]
# Pytest configuration for AI Agents Testing and Debugging

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Markers for test categorization
markers =
    llm_integration: Tests for LLM integration with OpenRouter API
    end_to_end: End-to-end workflow tests
    api_validation: API response validation tests
    error_handling: Error handling and debugging tests
    performance: Performance and load testing
    integration: Multi-agent coordination tests
    websocket: WebSocket communication tests
    slow: Tests that take a long time to run
    requires_api_key: Tests that require OpenRouter API key
    requires_network: Tests that require network connectivity

# Test execution options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --durations=10
    --color=yes
    --json-report
    --json-report-summary
    --cov=app
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=80

# Async test support
asyncio_mode = auto

# Timeout settings
timeout = 300
timeout_method = thread

# Logging configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Filter warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:httpx.*
    ignore::UserWarning:openai.*

# Minimum Python version
minversion = 3.8

# Test collection timeout
collect_timeout = 60

# Parallel execution (if pytest-xdist is installed)
# addopts = -n auto

# Environment variables for testing
env =
    TESTING = true
    LOG_LEVEL = DEBUG
    OPENROUTER_API_KEY = ${OPENROUTER_API_KEY}
    DEFAULT_LLM_MODEL = qwen/qwen3-235b-a22b-thinking-2507
