# Multi-Agent Real-Estate Contract Platform - Environment Configuration
# Copy this file to .env and update the values for your environment

# Application Settings
APP_NAME="Multi-Agent Real-Estate Contract Platform"
VERSION="0.1.0"
ENVIRONMENT="development"  # development, staging, production
DEBUG="true"

# Server Settings
HOST="0.0.0.0"
PORT="8000"
ALLOWED_HOSTS="*"  # Use specific domains in production

# Database Settings
DATABASE_URL="sqlite:///./database.db"  # Use PostgreSQL in production
DATABASE_ECHO="false"

# Redis Settings (for Celery and caching)
REDIS_URL="redis://localhost:6379/0"
CELERY_BROKER_URL="redis://localhost:6379/1"
CELERY_RESULT_BACKEND="redis://localhost:6379/2"

# JWT Settings
JWT_SECRET_KEY="dev-secret-key-change-in-production"  # MUST change in production
JWT_ALGORITHM="HS256"
JWT_ACCESS_TOKEN_EXPIRE_MINUTES="30"
JWT_REFRESH_TOKEN_EXPIRE_DAYS="7"

# S3/MinIO Settings
S3_ENDPOINT="http://localhost:9000"  # MinIO endpoint for development
S3_ACCESS_KEY="minioadmin"
S3_SECRET_KEY="minioadmin123"
S3_BUCKET_NAME="realestate-files"
S3_REGION="us-east-1"
S3_USE_SSL="false"  # Set to true in production

# File Upload Settings
MAX_FILE_SIZE="104857600"  # 100MB in bytes
ALLOWED_FILE_TYPES="pdf,docx,doc,png,jpg,jpeg,tiff"

# OCR Settings
TESSERACT_CMD=""  # Leave empty to use system default
OCR_LANGUAGES="eng"

# AI/LLM Settings
OPENAI_API_KEY=""  # Your OpenAI API key
ANTHROPIC_API_KEY=""  # Your Anthropic API key
OPENROUTER_API_KEY=""  # Your OpenRouter API key (unified access to 100+ models)
OLLAMA_BASE_URL="http://localhost:11434"

# Default AI Model Settings
DEFAULT_LLM_MODEL="gpt-4o-mini"
DEFAULT_EMBEDDING_MODEL="text-embedding-3-small"

# Model Router Settings
MODEL_ROUTER_STRATEGY="cost_optimized"  # Options: cost_optimized, performance, balanced
MODEL_ROUTER_FALLBACK_ENABLED="true"
MODEL_ROUTER_HEALTH_CHECK_INTERVAL="300"  # seconds
MODEL_ROUTER_MAX_RETRIES="3"

# E-signature Settings (DocuSign)
DOCUSIGN_INTEGRATION_KEY=""
DOCUSIGN_USER_ID=""
DOCUSIGN_ACCOUNT_ID=""
DOCUSIGN_PRIVATE_KEY=""
DOCUSIGN_BASE_URL="https://demo.docusign.net"  # Use https://www.docusign.net for production

# Logging Settings
LOG_LEVEL="INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Security Settings
BCRYPT_ROUNDS="12"
SESSION_EXPIRE_HOURS="24"

# Rate Limiting Settings
RATE_LIMIT_REQUESTS="100"
RATE_LIMIT_WINDOW="60"

# Development/Testing Settings
FASTAPI_ENV="development"
FASTAPI_DEBUG="true"
FASTAPI_RELOAD="true"
