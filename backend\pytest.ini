[tool:pytest]
# Pytest configuration for the Multi-Agent Real-Estate Contract Platform

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Output options
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    --durations=10

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow tests
    auth: Authentication tests
    database: Database tests
    files: File operation tests
    contracts: Contract management tests
    ai_agents: AI agent tests
    admin: Admin functionality tests

# Minimum version
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Coverage options (when using pytest-cov)
# addopts = --cov=app --cov-report=html --cov-report=term-missing

# Asyncio configuration
asyncio_mode = auto

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Filterwarnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*unclosed.*:ResourceWarning
